#!/usr/bin/env python3
# coding: utf-8

import time
import json
from pathlib import Path
from typing import List, Dict, <PERSON>ple

import numpy as np
from PIL import Image, ImageDraw, ImageFont, UnidentifiedImageError
import torch
from paddleocr import PaddleOCR
from transformers import Qwen2_5_VLForConditionalGeneration, AutoProcessor
from qwen_vl_utils import process_vision_info

from chain import HandwritingOCR

class WebHandwritingOCR(HandwritingOCR):
    """Extended HandwritingOCR class for web interface with bbox visualization"""
    
    def __init__(self, prompt: str = "Please recognize the handwriting and output only the text:", 
                 lang: str = "ch", det_thresh: float = 0.3, box_thresh: float = 0.6):
        super().__init__(prompt, lang, det_thresh, box_thresh)
        
    def run_with_visualization(self, image_path: str) -> Dict:
        """Run OCR with detailed bbox information for web visualization"""
        img_path = Path(image_path)
        if not img_path.exists():
            raise FileNotFoundError(img_path)

        try:
            pil_img = Image.open(img_path).convert("RGB")
        except UnidentifiedImageError:
            raise ValueError("unsupported image format")

        start = time.time()

        # 1. Detect text regions
        print(f"[DEBUG] Processing image: {img_path}")
        print(f"[DEBUG] Image size: {pil_img.size}")
        
        try:
            detect_out = self.detector.predict(str(img_path))
        except AttributeError:
            detect_out = self.detector.ocr(str(img_path))
        
        # Extract boxes with detailed information
        boxes = []
        raw_boxes = []
        
        if hasattr(detect_out, "boxes"):
            raw_boxes = [box for box in detect_out.boxes]
        elif isinstance(detect_out, list) and len(detect_out) > 0:
            first_result = detect_out[0]
            
            if hasattr(first_result, 'get') or isinstance(first_result, dict):
                if 'dt_polys' in first_result:
                    raw_boxes = first_result['dt_polys']
                elif hasattr(first_result, 'dt_polys'):
                    raw_boxes = first_result.dt_polys
            elif hasattr(first_result, "boxes"):
                raw_boxes = [box for box in first_result.boxes]
            else:
                for item in detect_out:
                    try:
                        if (item is not None and 
                            isinstance(item, (list, tuple)) and 
                            len(item) > 0 and 
                            item[0] is not None):
                            raw_boxes.append(np.asarray(item[0]))
                    except (IndexError, KeyError, TypeError, AttributeError):
                        continue

        if not raw_boxes:
            raise ValueError("no text detected")

        # Process and sort boxes
        for i, box in enumerate(raw_boxes):
            box_array = np.asarray(box)
            xmin, ymin = box_array.min(axis=0)
            xmax, ymax = box_array.max(axis=0)
            
            boxes.append({
                'id': i + 1,
                'polygon': box_array.tolist(),
                'bbox': [float(xmin), float(ymin), float(xmax), float(ymax)],
                'center_y': float(np.mean(box_array[:, 1])),
                'text': '',
                'confidence': 1.0
            })

        # Sort by y coordinate
        boxes.sort(key=lambda b: b['center_y'])
        
        # Update IDs after sorting
        for i, box in enumerate(boxes):
            box['id'] = i + 1

        # 2. Recognize text for each box
        print(f"[DEBUG] Starting Qwen2.5-VL recognition for {len(boxes)} text regions...")
        lines: List[str] = []
        
        for i, box_info in enumerate(boxes):
            print(f"[DEBUG] Processing box {i+1}/{len(boxes)}")
            try:
                box_array = np.asarray(box_info['polygon'])
                crop = self._crop(pil_img, box_array)
                recognized_text = self._recognize_line(crop)
                box_info['text'] = recognized_text
                lines.append(recognized_text)
                print(f"[DEBUG] Box {i+1} result: '{recognized_text}'")
            except Exception as e:
                error_text = f"[ERROR: {str(e)}]"
                box_info['text'] = error_text
                lines.append(error_text)
                print(f"[ERROR] Failed to process box {i+1}: {e}")

        elapsed = time.time() - start
        
        # Create annotated image
        annotated_image_path = self._create_annotated_image(pil_img, boxes, img_path)
        
        return {
            "text": "\n".join(lines),
            "lines": lines,
            "boxes": boxes,
            "time": elapsed,
            "image_size": pil_img.size,
            "annotated_image": annotated_image_path,
            "total_boxes": len(boxes)
        }
    
    def _create_annotated_image(self, pil_img: Image.Image, boxes: List[Dict], 
                              original_path: Path) -> str:
        """Create annotated image with numbered bounding boxes"""
        annotated_img = pil_img.copy()
        draw = ImageDraw.Draw(annotated_img)
        
        # Try to load a font, fallback to default if not available
        try:
            font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf", 24)
        except:
            try:
                font = ImageFont.truetype("arial.ttf", 24)
            except:
                font = ImageFont.load_default()
        
        colors = [
            '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', 
            '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
        ]
        
        for box_info in boxes:
            box_id = box_info['id']
            polygon = box_info['polygon']
            color = colors[(box_id - 1) % len(colors)]
            
            # Draw polygon outline
            points = [(float(p[0]), float(p[1])) for p in polygon]
            draw.polygon(points, outline=color, width=3)
            
            # Draw filled circle for number
            center_x = sum(p[0] for p in points) / len(points)
            center_y = sum(p[1] for p in points) / len(points)
            
            circle_radius = 15
            draw.ellipse([
                center_x - circle_radius, center_y - circle_radius,
                center_x + circle_radius, center_y + circle_radius
            ], fill=color, outline='white', width=2)
            
            # Draw number
            bbox = draw.textbbox((0, 0), str(box_id), font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            draw.text(
                (center_x - text_width/2, center_y - text_height/2), 
                str(box_id), 
                fill='white', 
                font=font
            )
        
        # Save annotated image
        output_dir = original_path.parent / "output"
        output_dir.mkdir(exist_ok=True)
        annotated_path = output_dir / f"{original_path.stem}_annotated{original_path.suffix}"
        annotated_img.save(annotated_path)
        
        return str(annotated_path) 
            'total_rows': len(rows),
            'boxes_per_row': [len(row) for row in rows],
            'sorting_algorithm': 'smart_row_grouping'
        }
        
        print(f"[STEP 2] Organized {len(boxes)} boxes into {len(rows)} rows")
        return sorted_boxes, reading_order_info
    
    def _intelligent_crop_regions(self, pil_img: Image.Image, boxes: List[np.ndarray]) -> List[Dict]:
        """
        Intelligent cropping with adaptive padding
        """
        print(f"[STEP 3] Intelligent cropping of {len(boxes)} regions...")
        
        cropped_regions = []
        
        for i, box in enumerate(boxes):
            try:
                # Calculate bounding rectangle
                x_coords = box[:, 0]
                y_coords = box[:, 1]
                
                min_x, max_x = int(np.min(x_coords)), int(np.max(x_coords))
                min_y, max_y = int(np.min(y_coords)), int(np.max(y_coords))
                
                # Calculate adaptive padding (10% of box size)
                width, height = max_x - min_x, max_y - min_y
                pad_x = max(5, int(width * 0.1))  # Minimum 5px padding
                pad_y = max(5, int(height * 0.1))
                
                # Apply padding with boundary checks
                crop_x1 = max(0, min_x - pad_x)
                crop_y1 = max(0, min_y - pad_y)
                crop_x2 = min(pil_img.width, max_x + pad_x)
                crop_y2 = min(pil_img.height, max_y + pad_y)
                
                # Crop the region
                cropped = pil_img.crop((crop_x1, crop_y1, crop_x2, crop_y2))
                
                crop_info = {
                    'index': i,
                    'image': cropped,
                    'bbox': (crop_x1, crop_y1, crop_x2, crop_y2),
                    'original_box': box,
                    'crop_size': f"{cropped.size[0]}x{cropped.size[1]}",
                    'padding': (pad_x, pad_y)
                }
                
                cropped_regions.append(crop_info)
                print(f"[DEBUG] Region {i+1}: {crop_info['crop_size']} (padding: {pad_x}x{pad_y})")
                
            except Exception as e:
                print(f"[ERROR] Failed to crop region {i+1}: {e}")
                cropped_regions.append({
                    'index': i,
                    'image': None,
                    'error': str(e),
                    'crop_size': 'error'
                })
        
        print(f"[STEP 3] Successfully cropped {len([c for c in cropped_regions if c.get('image')])}/{len(boxes)} regions")
        return cropped_regions
    
    def _qwen_recognition_only(self, cropped_regions: List[Dict]) -> List[Dict]:
        """
        Qwen2.5-VL recognition only - enhanced with confidence estimation
        """
        print(f"[STEP 4] Qwen2.5-VL recognition of {len(cropped_regions)} regions...")
        
        recognition_results = []
        
        for i, crop_info in enumerate(cropped_regions):
            if crop_info.get('image') is None:
                recognition_results.append({
                    'text': f"[ERROR: {crop_info.get('error', 'Unknown error')}]",
                    'confidence': 0.0,
                    'processing_time': 0.0,
                    'error': True
                })
                continue
            
            try:
                start_time = time.time()
                
                # Prepare Qwen2.5-VL input
                messages = [
                    {
                        "role": "user",
                        "content": [
                            {"type": "image", "image": crop_info['image']},
                            {"type": "text", "text": self.prompt},
                        ],
                    }
                ]
                
                text_prompt = self.processor.apply_chat_template(
                    messages, tokenize=False, add_generation_prompt=True
                )
                img_inputs, vid_inputs = process_vision_info(messages)
                inputs = self.processor(
                    text=[text_prompt],
                    images=img_inputs,
                    videos=vid_inputs,
                    padding=True,
                    return_tensors="pt",
                ).to(self.model.device)

                out_ids = self.model.generate(
                    **inputs,
                    max_new_tokens=8000,
                    temperature=0.1,
                    pad_token_id=self.processor.tokenizer.eos_token_id,
                )
                out_ids = out_ids[:, inputs.input_ids.shape[1] :]
                recognized_text = self.processor.batch_decode(
                    out_ids, skip_special_tokens=True, clean_up_tokenization_spaces=False
                )[0].strip()
                
                processing_time = time.time() - start_time
                
                # Enhanced confidence estimation
                confidence = self._estimate_confidence(recognized_text, processing_time, crop_info)
                
                recognition_results.append({
                    'text': recognized_text,
                    'confidence': confidence,
                    'processing_time': processing_time,
                    'error': False
                })
                
                print(f"[DEBUG] Region {i+1}: '{recognized_text}' (conf: {confidence:.3f}, time: {processing_time:.2f}s)")
                
            except Exception as e:
                print(f"[ERROR] Recognition failed for region {i+1}: {e}")
                recognition_results.append({
                    'text': f"[ERROR: {str(e)}]",
                    'confidence': 0.0,
                    'processing_time': 0.0,
                    'error': True
                })
        
        success_count = len([r for r in recognition_results if not r.get('error', False)])
        print(f"[STEP 4] Recognition completed: {success_count}/{len(cropped_regions)} successful")
        return recognition_results
    
    def _estimate_confidence(self, text: str, processing_time: float, crop_info: Dict) -> float:
        """
        Enhanced confidence estimation based on multiple factors
        """
        if not text or text.startswith('[ERROR'):
            return 0.0
        
        # Base confidence from text characteristics
        text_length = len(text.strip())
        if text_length == 0:
            return 0.1
        
        # Factor 1: Text length (longer texts usually more reliable)
        length_factor = min(1.0, text_length / 10.0)
        
        # Factor 2: Processing time (reasonable time indicates good processing)
        time_factor = 1.0 if 0.5 <= processing_time <= 3.0 else 0.8
        
        # Factor 3: Crop quality (larger crops usually better)
        crop_size = crop_info.get('crop_size', '0x0')
        if 'x' in crop_size:
            try:
                w, h = map(int, crop_size.split('x'))
                size_factor = min(1.0, (w * h) / 10000.0)  # Normalize to 100x100
            except:
                size_factor = 0.5
        else:
            size_factor = 0.5
        
        # Factor 4: Text pattern (Chinese/English characters)
        pattern_factor = 1.0 if any('\u4e00' <= char <= '\u9fff' for char in text) else 0.9
        
        # Combine factors
        confidence = (length_factor * 0.3 + 
                     time_factor * 0.2 + 
                     size_factor * 0.2 + 
                     pattern_factor * 0.3)
        
        return min(0.95, max(0.1, confidence))
    
    def _get_confidence_level(self, confidence: float) -> str:
        """Get confidence level string"""
        if confidence >= 0.8:
            return 'high'
        elif confidence >= 0.5:
            return 'medium'
        else:
            return 'low'
    
    def _create_enhanced_visualization(self, pil_img: Image.Image, boxes: List[np.ndarray], 
                                     recognition_results: List[Dict], original_path: Path) -> str:
        """
        Create enhanced visualization with confidence color coding
        """
        print(f"[STEP 5] Creating enhanced visualization...")
        
        annotated_img = pil_img.copy()
        draw = ImageDraw.Draw(annotated_img)
        
        # Load font
        try:
            font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf", 24)
        except:
            font = ImageFont.load_default()
        
        # Confidence-based colors
        confidence_colors = {
            'high': '#22c55e',    # Green
            'medium': '#f59e0b',  # Orange  
            'low': '#ef4444'      # Red
        }
        
        for i, (box_coords, result) in enumerate(zip(boxes, recognition_results)):
            box_id = i + 1
            confidence = result.get('confidence', 0.0)
            confidence_level = self._get_confidence_level(confidence)
            color = confidence_colors[confidence_level]
            
            # Draw polygon outline with confidence color
            points = [(float(p[0]), float(p[1])) for p in box_coords]
            draw.polygon(points, outline=color, width=3)
            
            # Draw filled circle for number
            center_x = sum(p[0] for p in points) / len(points)
            center_y = sum(p[1] for p in points) / len(points)
            
            circle_radius = 18
            draw.ellipse([
                center_x - circle_radius, center_y - circle_radius,
                center_x + circle_radius, center_y + circle_radius
            ], fill=color, outline='white', width=2)
            
            # Draw number
            bbox = draw.textbbox((0, 0), str(box_id), font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            draw.text(
                (center_x - text_width/2, center_y - text_height/2), 
                str(box_id), 
                fill='white', 
                font=font
            )
        
        # Add legend
        legend_y = 30
        legend_items = [
            ('High Confidence (≥0.8)', confidence_colors['high']),
            ('Medium Confidence (0.5-0.8)', confidence_colors['medium']),
            ('Low Confidence (<0.5)', confidence_colors['low'])
        ]
        
        for i, (label, color) in enumerate(legend_items):
            y_pos = legend_y + i * 35
            # Draw color indicator
            draw.rectangle([20, y_pos, 40, y_pos + 20], fill=color)
            # Draw label
            draw.text([50, y_pos], label, fill='black', font=font)
        
        # Save annotated image
        output_dir = original_path.parent / "output"
        output_dir.mkdir(exist_ok=True)
        annotated_path = output_dir / f"{original_path.stem}_enhanced_annotated{original_path.suffix}"
        annotated_img.save(annotated_path)
        
        print(f"[STEP 5] Enhanced visualization saved: {annotated_path}")
        return str(annotated_path)
    
    def _generate_detailed_statistics(self, detection_boxes, recognition_results, 
                                    detection_time, sorting_time, cropping_time,
                                    recognition_time, visualization_time, total_time,
                                    reading_order_info) -> Dict:
        """
        Generate detailed processing statistics
        """
        valid_results = [r for r in recognition_results if not r.get('error', False)]
        
        # Confidence distribution
        confidences = [r['confidence'] for r in valid_results]
        conf_dist = {
            'high': len([c for c in confidences if c >= 0.8]),
            'medium': len([c for c in confidences if 0.5 <= c < 0.8]),
            'low': len([c for c in confidences if c < 0.5])
        }
        
        statistics = {
            'timing': {
                'detection_time': round(detection_time, 3),
                'sorting_time': round(sorting_time, 3),
                'cropping_time': round(cropping_time, 3),
                'recognition_time': round(recognition_time, 3),
                'visualization_time': round(visualization_time, 3),
                'total_time': round(total_time, 3),
                'avg_time_per_region': round(total_time / len(detection_boxes), 3)
            },
            'detection': {
                'regions_detected': len(detection_boxes),
                'regions_recognized': len(valid_results),
                'success_rate': round(len(valid_results) / len(detection_boxes) * 100, 1)
            },
            'confidence': {
                'distribution': conf_dist,
                'average': round(np.mean(confidences) if confidences else 0, 3),
                'min': round(min(confidences) if confidences else 0, 3),
                'max': round(max(confidences) if confidences else 0, 3)
            },
            'reading_order': reading_order_info,
            'algorithm_version': 'enhanced_hybrid_v2.0'
        }
        
        print(f"[STATS] Success rate: {statistics['detection']['success_rate']}%")
        print(f"[STATS] Average confidence: {statistics['confidence']['average']}")
        print(f"[STATS] Total processing time: {statistics['timing']['total_time']}s")
        
        return statistics