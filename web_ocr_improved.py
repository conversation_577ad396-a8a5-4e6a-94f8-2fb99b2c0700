#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ImprovedWebHandwritingOCR (fixed)
---------------------------------
- 解决 "Image features and image tokens do not match" 报错
- 解决 feature_extraction_utils 中 "activate padding" 报错
- 规避 numpy.ndarray / torch.from_numpy TypeError（可选补丁）
- 单图推理，逐块释放显存，避免批内 shape 不一致
- 其它流程、可视化、统计功能保持完整

NOTE:
1) 本文件不处理 CUDA OOM（内存不足）问题，按你的要求忽略。
2) 依赖:
   - transformers >= 4.41 (官方 Qwen2.5-VL 支持)
   - qwen-vl-utils (随模型仓库附带)
   - paddleocr==2.7+ 仅用 TextDetection
   - torch 2.x
"""

import os
import time
from pathlib import Path
from typing import List, Dict, Tuple

import numpy as np
from PIL import Image, ImageDraw, ImageFont, UnidentifiedImageError
import torch
from paddleocr import TextDetection
from transformers import Qwen2_5_VLForConditionalGeneration, AutoProcessor

# 官方工具: 用于从 messages 中抽取 image/video 输入
try:
    from qwen_vl_utils import process_vision_info
except ImportError:
    # 兼容性: 如果你把 qwen_vl_utils.py 放在同目录，也可以直接导入本地文件
    from .qwen_vl_utils import process_vision_info  # type: ignore

# ---------------------------
# 可选: NumPy / torch 兼容补丁
# ---------------------------

def patch_torch_from_numpy_once() -> None:
    """避免 TypeError: expected np.ndarray (got numpy.ndarray)"""
    if getattr(torch.from_numpy, "_patched_for_numpy_compat", False):
        return

    _orig = torch.from_numpy

    def _safe_from_numpy(arr):
        try:
            return _orig(arr)
        except (TypeError, RuntimeError) as e:
            msg = str(e)
            if "expected np.ndarray" in msg or "numpy.ndarray" in msg:
                fixed = np.asarray(arr)
                return _orig(fixed)
            raise

    _safe_from_numpy._patched_for_numpy_compat = True  # type: ignore
    torch.from_numpy = _safe_from_numpy  # type: ignore


# 你的本地模型目录
LOCAL_MODEL_DIR = (
    "/home/<USER>/HandWriting_Qwen2.5-VL/models/"
    "Qwen/Qwen2___5-VL-7B-Instruct"
)


class ImprovedWebHandwritingOCR:
    """
    TextDetection 负责检测，Qwen2.5-VL 负责识别
    修复了前面遇到的 tokens/features 不匹配 & padding 报错
    """

    def __init__(
        self,
        prompt: str = "Please recognize the handwriting and output only the text:",
        lang: str = "ch",
        det_thresh: float = 0.3,
        box_thresh: float = 0.6,
        unclip_ratio: float = 2.0,
        image_size: int = 1280,
        overlap_threshold: float = 0.7,
    ):
        print("[INIT] Initializing Improved Hybrid OCR...")

        # 1) 文字检测 (CPU 足够)
        print("[INIT] Loading TextDetection (PP-OCRv5_server_det) for detection only...")
        self.detector = TextDetection(model_name="PP-OCRv5_server_det")

        # 2) 文字识别模型
        print("[INIT] Loading Qwen2.5-VL for recognition only...")
        self.model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
            LOCAL_MODEL_DIR,
            device_map="auto",
            torch_dtype="auto",
            trust_remote_code=True,
            local_files_only=True,
        ).eval()

        self.processor = AutoProcessor.from_pretrained(
            LOCAL_MODEL_DIR,
            trust_remote_code=True,
            local_files_only=True,
            use_fast=True,  # 如果有 fast tokenizer
        )

        # 保存参数
        self.prompt = prompt
        self.lang = lang
        self.det_thresh = det_thresh
        self.box_thresh = box_thresh
        self.unclip_ratio = unclip_ratio
        self.image_size = image_size
        self.merge_threshold = 0.07
        self.min_concat_count = 3
        self.overlap_threshold = overlap_threshold

        print("[INIT] TextDetection -> CPU")
        print(f"[INIT] Qwen2.5-VL Recognition -> {self.model.device}")
        print(
            f"[INIT] Parameters: thresh={det_thresh}, box_thresh={box_thresh}, "
            f"unclip_ratio={unclip_ratio}, image_size={image_size}, overlap_threshold={overlap_threshold}"
        )
        print("[INIT] Improved Hybrid OCR initialization complete!")

        # 兼容补丁
        patch_torch_from_numpy_once()

    # ---------------
    # 参数更新
    # ---------------
    def update_parameters(self, **kwargs):
        if 'det_thresh' in kwargs:
            self.det_thresh = kwargs['det_thresh']
        if 'box_thresh' in kwargs:
            self.box_thresh = kwargs['box_thresh']
        if 'unclip_ratio' in kwargs:
            self.unclip_ratio = kwargs['unclip_ratio']
        if 'image_size' in kwargs:
            self.image_size = kwargs['image_size']
        if 'prompt' in kwargs:
            self.prompt = kwargs['prompt']
        if 'lang' in kwargs:
            self.lang = kwargs['lang']
        if 'merge_threshold' in kwargs:
            self.merge_threshold = kwargs['merge_threshold']
            print(f"[UPDATE] merge_threshold -> {self.merge_threshold}")
        if 'min_concat_count' in kwargs:
            self.min_concat_count = kwargs['min_concat_count']
            print(f"[UPDATE] min_concat_count -> {self.min_concat_count}")
        if 'overlap_threshold' in kwargs:
            self.overlap_threshold = kwargs['overlap_threshold']
            print(f"[UPDATE] overlap_threshold -> {self.overlap_threshold}")

        print(
            f"[UPDATE] thresh={self.det_thresh}, box_thresh={self.box_thresh}, "
            f"unclip_ratio={self.unclip_ratio}, image_size={self.image_size}, "
            f"merge_threshold={self.merge_threshold}, overlap_threshold={self.overlap_threshold}, "
            f"min_concat_count={self.min_concat_count}"
        )

    # ---------------
    # 主流程
    # ---------------
    def run_with_visualization(self, image_path: str, custom_prompt: str = None) -> Dict:
        img_path = Path(image_path)
        if not img_path.exists():
            raise FileNotFoundError(f"Image not found: {img_path}")

        try:
            pil_img = Image.open(img_path).convert("RGB")
        except UnidentifiedImageError:
            raise ValueError("Unsupported image format")

        print("\n" + "=" * 60)
        print(f"IMPROVED HYBRID OCR ANALYSIS: {img_path.name}")
        print("=" * 60)
        print(f"Image size: {pil_img.width} x {pil_img.height} pixels")
        print(
            f"Current parameters: thresh={self.det_thresh}, box_thresh={self.box_thresh}, "
            f"unclip_ratio={self.unclip_ratio}, image_size={self.image_size}"
        )

        total_start = time.time()

        # Step 1: 检测
        print("\n[STEP 1] TextDetection...")
        det_start = time.time()
        detection_boxes = self._text_detection_only(str(img_path))
        det_time = time.time() - det_start
        print(f"[STEP 1] Detection completed: {len(detection_boxes)} regions in {det_time:.2f}s")
        if not detection_boxes:
            raise ValueError("No text regions detected")

        # Step 1.5: 重叠合并
        print("\n[STEP 1.5] Merging Overlapping Boxes...")
        ov_start = time.time()
        merged_det_boxes = self._merge_overlapping_boxes(detection_boxes)
        ov_time = time.time() - ov_start
        print(f"[STEP 1.5] Overlap merge completed: {len(detection_boxes)} -> {len(merged_det_boxes)} boxes in {ov_time:.3f}s")

        # Step 2: 排序
        print("\n[STEP 2] Smart Reading Order Sorting...")
        sort_start = time.time()
        sorted_boxes = self._sort_boxes_reading_order(merged_det_boxes)
        sort_time = time.time() - sort_start
        print(f"[STEP 2] Sorting completed in {sort_time:.3f}s")

        # Step 3: 合并文本块
        print("\n[STEP 3] Merging Text Regions with Min Concat Count...")
        merge_start = time.time()
        merged_regions = self._merge_by_column_with_min_count(sorted_boxes)
        merge_time = time.time() - merge_start
        print(f"[STEP 3] Merged {len(sorted_boxes)} boxes into {len(merged_regions)} regions in {merge_time:.3f}s")

        # Step 4: 裁剪
        print("\n[STEP 4] Smart Text Region Cropping...")
        crop_start = time.time()
        cropped_images = self._crop_merged_regions_smart(pil_img, merged_regions)
        crop_time = time.time() - crop_start
        valid_crops = sum(1 for c in cropped_images if c['image'] is not None)
        print(f"[STEP 4] Cropped {valid_crops}/{len(merged_regions)} regions in {crop_time:.3f}s")

        # Step 5: 识别
        print("\n[STEP 5] Qwen2.5-VL Text Recognition...")
        if custom_prompt:
            print(f"Using custom prompt: {custom_prompt}")
        rec_start = time.time()
        rec_results = self._qwen_recognition_detailed(cropped_images, custom_prompt)
        rec_time = time.time() - rec_start
        print(f"[STEP 5] Recognition completed in {rec_time:.2f}s")

        # Step 6: 可视化
        print("\n[STEP 6] Creating Visualizations...")
        viz_start = time.time()
        annotated_path = self._create_enhanced_annotated_image(pil_img, merged_regions, rec_results, img_path)
        viz_time = time.time() - viz_start
        print(f"[STEP 6] Visualization created in {viz_time:.3f}s")

        total_time = time.time() - total_start

        processed_boxes = self._prepare_box_results_merged(merged_regions, rec_results)
        stats = self._calculate_detailed_statistics(merged_det_boxes, rec_results, det_time, rec_time, total_time)

        print(f"\n[SUMMARY] Total processing time: {total_time:.2f}s")
        print(f"[SUMMARY] Success rate: {stats['success_rate']:.1f}%")
        print(f"[SUMMARY] Average confidence: {stats['avg_confidence']:.3f}")

        return {
            "text": "\n".join([r['text'] for r in rec_results if r['status'] == 'success']),
            "lines": [r['text'] for r in rec_results],
            "boxes": processed_boxes,
            "time": float(total_time),
            "image_size": [int(pil_img.width), int(pil_img.height)],
            "annotated_image": annotated_path,
            "total_boxes": int(len(merged_regions)),
            "original_boxes": int(len(detection_boxes)),
            "statistics": stats,
            "processing_steps": {
                "detection_time": float(det_time),
                "overlap_merge_time": float(ov_time),
                "sorting_time": float(sort_time),
                "merging_time": float(merge_time),
                "cropping_time": float(crop_time),
                "recognition_time": float(rec_time),
                "visualization_time": float(viz_time),
            },
        }

    # -----------------
    # 文字检测
    # -----------------
    def _text_detection_only(self, image_path: str) -> List[np.ndarray]:
        try:
            result = self.detector.predict(
                input=image_path,
                batch_size=1,
                thresh=max(0.1, self.det_thresh),
                box_thresh=max(0.3, self.box_thresh),
                unclip_ratio=self.unclip_ratio,
                limit_side_len=self.image_size,
                limit_type="max",
            )
            boxes = []
            if result and len(result) > 0 and isinstance(result[0], dict):
                polys = result[0].get('dt_polys', [])
                for poly in polys:
                    if poly is not None and len(poly) >= 4:
                        arr = np.array(poly).reshape(-1, 2)
                        if arr.shape[0] == 4:
                            boxes.append(arr)
            print(f"[DEBUG] Successfully extracted {len(boxes)} valid text regions")
            return boxes
        except Exception as e:
            print(f"[ERROR] TextDetection failed: {e}")
            import traceback
            traceback.print_exc()
            return []

    # -----------------
    # 合并重叠框
    # -----------------
    def _merge_overlapping_boxes(self, boxes: List[np.ndarray]) -> List[np.ndarray]:
        if not boxes or len(boxes) <= 1:
            return boxes
        print(f"[DEBUG] 开始合并重叠框，原始框数量：{len(boxes)}，阈值：{self.overlap_threshold:.0%}")

        rects = []
        for i, b in enumerate(boxes):
            xs, ys = b[:, 0], b[:, 1]
            min_x, max_x = xs.min(), xs.max()
            min_y, max_y = ys.min(), ys.max()
            rects.append({
                'index': i, 'box': b,
                'min_x': min_x, 'max_x': max_x,
                'min_y': min_y, 'max_y': max_y,
                'width': max_x - min_x, 'height': max_y - min_y,
                'area': (max_x - min_x) * (max_y - min_y)
            })

        merged_groups: List[List[Dict]] = []
        processed = set()
        for i, r1 in enumerate(rects):
            if i in processed:
                continue
            group = [r1]
            processed.add(i)
            for j, r2 in enumerate(rects):
                if j in processed or i == j:
                    continue
                if self._calculate_overlap_ratio(r1, r2) >= self.overlap_threshold:
                    group.append(r2)
                    processed.add(j)
                    self._find_overlapping_boxes_recursive(r2, rects, group, processed)
            merged_groups.append(group)

        merged_boxes = []
        merge_cnt = 0
        for g in merged_groups:
            if len(g) == 1:
                merged_boxes.append(g[0]['box'])
            else:
                merged_boxes.append(self._create_merged_box_from_group(g))
                merge_cnt += len(g) - 1
        print(f"[DEBUG] 重叠框合并完成：{len(boxes)} -> {len(merged_boxes)}，合并了 {merge_cnt} 个重叠框")
        return merged_boxes

    def _find_overlapping_boxes_recursive(self, cur: Dict, all_rects: List[Dict], group: List[Dict], processed: set):
        for i, r in enumerate(all_rects):
            if i in processed:
                continue
            if self._calculate_overlap_ratio(cur, r) >= self.overlap_threshold:
                group.append(r)
                processed.add(i)
                self._find_overlapping_boxes_recursive(r, all_rects, group, processed)

    @staticmethod
    def _calculate_overlap_ratio(r1: Dict, r2: Dict) -> float:
        left = max(r1['min_x'], r2['min_x'])
        right = min(r1['max_x'], r2['max_x'])
        top = max(r1['min_y'], r2['min_y'])
        bottom = min(r1['max_y'], r2['max_y'])
        if left >= right or top >= bottom:
            return 0.0
        inter = (right - left) * (bottom - top)
        smaller = min(r1['area'], r2['area'])
        return inter / smaller if smaller > 0 else 0.0

    @staticmethod
    def _create_merged_box_from_group(group: List[Dict]) -> np.ndarray:
        min_x = min(r['min_x'] for r in group)
        max_x = max(r['max_x'] for r in group)
        min_y = min(r['min_y'] for r in group)
        max_y = max(r['max_y'] for r in group)
        return np.array([[min_x, min_y], [max_x, min_y], [max_x, max_y], [min_x, max_y]], dtype=np.float32)

    # -----------------
    # 排序 & 合并
    # -----------------
    def _sort_boxes_reading_order(self, boxes: List[np.ndarray]) -> List[np.ndarray]:
        if not boxes:
            return boxes
        info = []
        for i, b in enumerate(boxes):
            xs, ys = b[:, 0], b[:, 1]
            info.append({
                'index': i,
                'center_x': xs.mean(),
                'center_y': ys.mean(),
                'min_x': xs.min(), 'max_x': xs.max(),
                'box': b
            })
        sorted_by_x = sorted(info, key=lambda x: x['center_x'])
        columns: List[List[Dict]] = []
        used = set()
        while len(used) < len(info):
            leftmost = next((x for x in sorted_by_x if x['index'] not in used), None)
            if leftmost is None:
                break
            col = [leftmost]
            used.add(leftmost['index'])
            c_left, c_right = leftmost['min_x'], leftmost['max_x']
            for item in info:
                if item['index'] in used:
                    continue
                overlap = min(c_right, item['max_x']) - max(c_left, item['min_x'])
                min_w = min(c_right - c_left, item['max_x'] - item['min_x'])
                if overlap > 0.3 * min_w:
                    col.append(item)
                    used.add(item['index'])
                    c_left = min(c_left, item['min_x'])
                    c_right = max(c_right, item['max_x'])
            columns.append(col)
        columns.sort(key=lambda c: min(x['center_x'] for x in c))

        sorted_boxes: List[np.ndarray] = []
        for i, col in enumerate(columns):
            col.sort(key=lambda x: x['center_y'])
            sorted_boxes.extend([x['box'] for x in col])
            avg_x = sum(x['center_x'] for x in col) / len(col)
            print(f"[DEBUG] Column {i+1}: {len(col)} boxes, avg_x: {avg_x:.1f}, x_range: {min(x['min_x'] for x in col):.1f}-{max(x['max_x'] for x in col):.1f}")
        print(f"[DEBUG] Organized into {len(columns)} columns.")
        return sorted_boxes

    def _merge_adjacent_text_regions(self, boxes: List[np.ndarray]) -> List[Dict]:
        if not boxes:
            return []
        info = []
        for i, b in enumerate(boxes):
            xs, ys = b[:, 0], b[:, 1]
            info.append({
                'index': i, 'box': b,
                'center_x': xs.mean(), 'center_y': ys.mean(),
                'min_x': xs.min(), 'max_x': xs.max(),
                'min_y': ys.min(), 'max_y': ys.max(),
                'width': xs.max() - xs.min(), 'height': ys.max() - ys.min(),
                'merged': False
            })
        merged_regions = []
        for it in info:
            if it['merged']:
                continue
            region_boxes = [it]
            it['merged'] = True
            changed = True
            while changed:
                changed = False
                bounds = self._calculate_region_bounds(region_boxes)
                for cand in info:
                    if cand['merged']:
                        continue
                    if self._can_merge_boxes(bounds, cand):
                        region_boxes.append(cand)
                        cand['merged'] = True
                        changed = True
            final_bounds = self._calculate_region_bounds(region_boxes)
            merged_regions.append({
                'merged_bbox': final_bounds,
                'original_boxes': [x['box'] for x in region_boxes],
                'box_count': len(region_boxes),
                'region_id': len(merged_regions)
            })
        print(f"[DEBUG] Merged {len(boxes)} individual boxes into {len(merged_regions)} regions")
        return merged_regions

    def _merge_by_column_with_min_count(self, boxes: List[np.ndarray]) -> List[Dict]:
        if not boxes or self.min_concat_count <= 1:
            return self._merge_adjacent_text_regions(boxes)
        print(f"[DEBUG] Column-based merge with min_concat_count={self.min_concat_count}")
        columns = self._group_boxes_by_column(boxes)
        print(f"[DEBUG] Detected {len(columns)} columns")
        merged_regions: List[Dict] = []
        for idx, col in enumerate(columns):
            print(f"[DEBUG] Column {idx+1}, {len(col)} boxes")
            if len(col) <= self.min_concat_count:
                merged_regions.append(self._create_merged_region(col))
            else:
                merged_regions.extend(self._merge_column_by_groups(col))
        print(f"[DEBUG] Column-based merge done, {len(merged_regions)} regions")
        return merged_regions

    def _group_boxes_by_column(self, boxes: List[np.ndarray]) -> List[List[Dict]]:
        info = []
        for i, b in enumerate(boxes):
            xs, ys = b[:, 0], b[:, 1]
            info.append({
                'index': i, 'box': b,
                'center_x': xs.mean(), 'center_y': ys.mean(),
                'min_x': xs.min(), 'max_x': xs.max(),
                'min_y': ys.min(), 'max_y': ys.max(),
                'width': xs.max() - xs.min(), 'height': ys.max() - ys.min()
            })
        sorted_by_x = sorted(info, key=lambda x: x['center_x'])
        columns, used = [], set()
        while len(used) < len(info):
            leftmost = next((x for x in sorted_by_x if x['index'] not in used), None)
            if leftmost is None:
                break
            col = [leftmost]
            used.add(leftmost['index'])
            c_left, c_right = leftmost['min_x'], leftmost['max_x']
            for item in info:
                if item['index'] in used:
                    continue
                overlap = min(c_right, item['max_x']) - max(c_left, item['min_x'])
                min_w = min(c_right - c_left, item['max_x'] - item['min_x'])
                if overlap > 0.3 * min_w:
                    col.append(item)
                    used.add(item['index'])
                    c_left = min(c_left, item['min_x'])
                    c_right = max(c_right, item['max_x'])
            col.sort(key=lambda x: x['center_y'])
            columns.append(col)
        columns.sort(key=lambda c: min(x['center_x'] for x in c))
        return columns

    def _merge_column_by_groups(self, col_boxes: List[Dict]) -> List[Dict]:
        out = []
        i = 0
        while i < len(col_boxes):
            group = col_boxes[i:i + self.min_concat_count]
            out.append(self._create_merged_region(group))
            i += self.min_concat_count
        return out

    @staticmethod
    def _create_merged_region(group: List[Dict]) -> Dict:
        if not group:
            return {}
        min_x = min(b['min_x'] for b in group)
        max_x = max(b['max_x'] for b in group)
        min_y = min(b['min_y'] for b in group)
        max_y = max(b['max_y'] for b in group)
        bbox = {
            'min_x': min_x, 'max_x': max_x,
            'min_y': min_y, 'max_y': max_y,
            'center_x': (min_x + max_x) / 2,
            'center_y': (min_y + max_y) / 2,
            'width': max_x - min_x,
            'height': max_y - min_y,
        }
        return {
            'merged_bbox': bbox,
            'original_boxes': [b['box'] for b in group],
            'box_count': len(group),
            'region_id': len(group),
        }

    @staticmethod
    def _calculate_region_bounds(region_boxes: List[Dict]) -> Dict:
        min_x = min(b['min_x'] for b in region_boxes)
        max_x = max(b['max_x'] for b in region_boxes)
        min_y = min(b['min_y'] for b in region_boxes)
        max_y = max(b['max_y'] for b in region_boxes)
        return {
            'min_x': min_x, 'max_x': max_x,
            'min_y': min_y, 'max_y': max_y,
            'center_x': (min_x + max_x) / 2,
            'center_y': (min_y + max_y) / 2,
            'width': max_x - min_x, 'height': max_y - min_y,
        }

    def _can_merge_boxes(self, bounds: Dict, cand: Dict) -> bool:
        avg_h = (bounds['height'] + cand['height']) / 2
        v_th = avg_h * self.merge_threshold
        h_overlap = min(bounds['max_x'], cand['max_x']) - max(bounds['min_x'], cand['min_x'])
        min_w = min(bounds['width'], cand['width'])
        if h_overlap > 0.4 * min_w:
            v_gap = min(abs(bounds['min_y'] - cand['max_y']), abs(cand['min_y'] - bounds['max_y']))
            if v_gap <= v_th:
                return True
        return False

    # -----------------
    # 裁剪
    # -----------------
    def _crop_merged_regions_smart(self, pil_img: Image.Image, merged_regions: List[Dict]) -> List[Dict]:
        crops = []
        for i, region in enumerate(merged_regions):
            try:
                b = region['merged_bbox']
                x1, y1 = int(b['min_x']), int(b['min_y'])
                x2, y2 = int(b['max_x']), int(b['max_y'])
                crop = pil_img.crop((x1, y1, x2, y2))
                crops.append({
                    'index': i,
                    'image': crop,
                    'bbox': (x1, y1, x2, y2),
                    'merged_region': region,
                    'size': crop.size,
                    'box_count': region['box_count']
                })
            except Exception as e:
                print(f"[ERROR] Failed to crop merged region {i+1}: {e}")
                crops.append({'index': i, 'image': None, 'bbox': None, 'merged_region': region, 'error': str(e)})
        return crops

    # -----------------
    # Qwen 识别 (关键修复)
    # -----------------
    def _qwen_recognition_detailed(self, cropped_images: List[Dict], custom_prompt: str = None) -> List[Dict]:
        results = []
        prompt = custom_prompt if custom_prompt is not None else self.prompt
        device = self.model.device

        for crop in cropped_images:
            if crop['image'] is None:
                results.append({
                    'text': f"[ERROR: {crop.get('error', 'Unknown')}]",
                    'confidence': 0.0,
                    'processing_time': 0.0,
                    'status': 'error'
                })
                continue

            w, h = crop['image'].size
            if w < 4 or h < 4:
                results.append({
                    'text': '[ERROR: Image too small]',
                    'confidence': 0.0,
                    'processing_time': 0.0,
                    'status': 'error'
                })
                continue

            start = time.time()
            try:
                # 1) 构建 messages
                messages = [{
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {"type": "image", "image": crop['image']},
                    ],
                }]

                # 2) 使用官方推荐流程，确保 tokens/features 对齐
                text = self.processor.apply_chat_template(
                    messages, tokenize=False, add_generation_prompt=True
                )
                img_inputs, vid_inputs = process_vision_info(messages)

                inputs = self.processor(
                    text=[text],
                    images=img_inputs["images"] if isinstance(img_inputs, dict) else img_inputs,
                    videos=vid_inputs.get("videos", []) if isinstance(vid_inputs, dict) else vid_inputs,
                    padding=True,
                    return_tensors="pt",
                )

                # 移动到设备
                inputs = {k: v.to(device) if torch.is_tensor(v) else v for k, v in inputs.items()}

                # 3) 生成 (避免 expand 相关问题：beam=1, do_sample=False)
                with torch.no_grad():
                    out_ids = self.model.generate(
                        **inputs,
                        max_new_tokens=128,
                        do_sample=False,
                        num_beams=1,
                        pad_token_id=self.processor.tokenizer.pad_token_id,
                        eos_token_id=self.processor.tokenizer.eos_token_id,
                        use_cache=True,
                        output_attentions=False,
                        output_hidden_states=False,
                        return_dict_in_generate=False,
                    )

                # 4) 解码
                in_len = inputs['input_ids'].shape[1]
                gen_tokens = out_ids[:, in_len:]
                text_out = self.processor.batch_decode(
                    gen_tokens, skip_special_tokens=True, clean_up_tokenization_spaces=True
                )[0].strip()

                proc_time = time.time() - start
                conf = self._calculate_confidence(text_out, proc_time, crop)
                results.append({
                    'text': text_out,
                    'confidence': conf,
                    'processing_time': proc_time,
                    'status': 'success'
                })

            except ValueError as e:
                # 特别处理 tokens/features 不匹配和 padding 报错
                msg = str(e)
                if "Image features and image tokens do not match" in msg:
                    print("[WARN] Token/feature mismatch, retrying with rebuilt inputs...")
                    try:
                        # 退回到单张图片 + 不用 chat_template 的保底方案
                        # 直接走官方 processor.chat 风格（如果可用）或重新构造 inputs
                        messages = [{
                            "role": "user",
                            "content": [
                                {"type": "image", "image": crop['image']},
                                {"type": "text", "text": prompt},
                            ],
                        }]
                        text = self.processor.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)
                        img_inputs, vid_inputs = process_vision_info(messages)
                        inputs = self.processor(
                            text=[text],
                            images=img_inputs["images"] if isinstance(img_inputs, dict) else img_inputs,
                            videos=vid_inputs.get("videos", []) if isinstance(vid_inputs, dict) else vid_inputs,
                            padding=True,
                            return_tensors="pt",
                        )
                        inputs = {k: v.to(device) if torch.is_tensor(v) else v for k, v in inputs.items()}
                        with torch.no_grad():
                            out_ids = self.model.generate(
                                **inputs,
                                max_new_tokens=128,
                                do_sample=False,
                                num_beams=1,
                                pad_token_id=self.processor.tokenizer.pad_token_id,
                                eos_token_id=self.processor.tokenizer.eos_token_id,
                            )
                        in_len = inputs['input_ids'].shape[1]
                        gen_tokens = out_ids[:, in_len:]
                        text_out = self.processor.batch_decode(gen_tokens, skip_special_tokens=True)[0].strip()
                        proc_time = time.time() - start
                        conf = self._calculate_confidence(text_out, proc_time, crop)
                        results.append({'text': text_out, 'confidence': conf, 'processing_time': proc_time, 'status': 'success'})
                        continue
                    except Exception as e2:
                        msg = f"[ERROR: {msg} | RETRY_FAIL: {e2}]"
                        results.append({'text': msg, 'confidence': 0.0, 'processing_time': 0.0, 'status': 'error'})
                        continue
                elif "Unable to create tensor" in msg:
                    msg = f"[ERROR: {msg}]"
                    results.append({'text': msg, 'confidence': 0.0, 'processing_time': 0.0, 'status': 'error'})
                    continue
                else:
                    msg = f"[ERROR: {msg}]"
                    results.append({'text': msg, 'confidence': 0.0, 'processing_time': 0.0, 'status': 'error'})
            except Exception as e:
                print(f"[ERROR] Region {crop['index']+1} failed: {e}")
                import traceback
                traceback.print_exc()
                results.append({'text': f"[ERROR: {e}]", 'confidence': 0.0, 'processing_time': 0.0, 'status': 'error'})
            finally:
                # 逐块释放显存（忽略 OOM 的大问题）
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()

        return results

    # -----------------
    # 计算 confidence
    # -----------------
    def _calculate_confidence(self, text: str, processing_time: float, crop_info: Dict) -> float:
        if not text or text.startswith('[ERROR'):
            return 0.0
        base = min(0.95, max(0.1, len(text.strip()) / 20))
        if 0.5 <= processing_time <= 3.0:
            time_f = 1.0
        elif processing_time < 0.5:
            time_f = 0.8
        else:
            time_f = 0.9
        if crop_info.get('size'):
            w, h = crop_info['size']
            size_f = 1.0 if (w * h) > 1000 else 0.8
        else:
            size_f = 0.7
        quality_f = 0.3 if any(k in text for k in ['[', ']', 'ERROR']) else (0.5 if len(text.strip()) < 2 else 1.0)
        conf = base * time_f * size_f * quality_f
        return float(min(0.95, max(0.05, conf)))

    # -----------------
    # 结果整理 / 统计 / 可视化
    # -----------------
    def _prepare_box_results_merged(self, merged_regions: List[Dict], rec: List[Dict]) -> List[Dict]:
        out = []
        for i, (region, res) in enumerate(zip(merged_regions, rec)):
            b = region['merged_bbox']
            color = '#22c55e' if res['confidence'] >= 0.8 else ('#f59e0b' if res['confidence'] >= 0.5 else '#ef4444')
            polygon = [
                [float(b['min_x']), float(b['min_y'])],
                [float(b['max_x']), float(b['min_y'])],
                [float(b['max_x']), float(b['max_y'])],
                [float(b['min_x']), float(b['max_y'])],
            ]
            out.append({
                'id': i + 1,
                'polygon': polygon,
                'bbox': [float(b['min_x']), float(b['min_y']), float(b['max_x']), float(b['max_y'])],
                'center_y': float(b['center_y']),
                'text': res['text'],
                'confidence': float(res['confidence']),
                'processing_time': float(res['processing_time']),
                'status': res['status'],
                'color': color,
                'box_count': int(region['box_count']),
                'original_boxes': [[[float(p[0]), float(p[1])] for p in ob.tolist()] for ob in region['original_boxes']],
                'is_merged': True,
            })
        return out

    @staticmethod
    def _calculate_detailed_statistics(detection_boxes: List, rec_results: List[Dict], det_t: float, rec_t: float, total_t: float) -> Dict:
        valid = [r for r in rec_results if r['status'] == 'success']
        if valid:
            confs = [r['confidence'] for r in valid]
            avg_c = float(np.mean(confs))
            min_c = float(min(confs))
            max_c = float(max(confs))
            high = sum(1 for c in confs if c >= 0.8)
            mid = sum(1 for c in confs if 0.5 <= c < 0.8)
            low = sum(1 for c in confs if c < 0.5)
        else:
            avg_c = min_c = max_c = 0.0
            high = mid = low = 0
        return {
            'total_regions': len(detection_boxes),
            'successful_recognitions': len(valid),
            'success_rate': float(len(valid) / len(detection_boxes) * 100 if detection_boxes else 0.0),
            'avg_confidence': avg_c,
            'min_confidence': min_c,
            'max_confidence': max_c,
            'confidence_distribution': {
                'high': int(high), 'medium': int(mid), 'low': int(low)
            },
            'timing': {
                'detection': float(det_t),
                'recognition': float(rec_t),
                'total': float(total_t),
                'avg_per_region': float(total_t / len(detection_boxes) if detection_boxes else 0.0),
            },
        }

    def _create_enhanced_annotated_image(self, pil_img: Image.Image, merged_regions: List[Dict], rec_results: List[Dict], original_path: Path) -> str:
        img = pil_img.copy()
        draw = ImageDraw.Draw(img)
        # 字体
        try:
            font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf", 24)
            small_font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", 16)
        except Exception:
            try:
                font = ImageFont.truetype("arial.ttf", 24)
                small_font = ImageFont.truetype("arial.ttf", 16)
            except Exception:
                font = ImageFont.load_default()
                small_font = ImageFont.load_default()

        for i, (region, res) in enumerate(zip(merged_regions, rec_results)):
            b = region['merged_bbox']
            color = '#22c55e' if res['confidence'] >= 0.8 else ('#f59e0b' if res['confidence'] >= 0.5 else '#ef4444')
            pts = [(b['min_x'], b['min_y']), (b['max_x'], b['min_y']), (b['max_x'], b['max_y']), (b['min_x'], b['max_y'])]
            draw.polygon(pts, outline=color, width=4)
            for ob in region['original_boxes']:
                draw.polygon([(float(p[0]), float(p[1])) for p in ob], outline=color, width=1)
            cx, cy = b['center_x'], b['center_y']
            r = 18
            draw.ellipse([cx - r, cy - r, cx + r, cy + r], fill=color, outline='white', width=2)
            tb = draw.textbbox((0, 0), str(i + 1), font=font)
            tw, th = tb[2] - tb[0], tb[3] - tb[1]
            draw.text((cx - tw / 2, cy - th / 2), str(i + 1), fill='white', font=font)
            conf_text = f"{res['confidence']:.2f} [Merged{region['box_count']}]"
            cb = draw.textbbox((0, 0), conf_text, font=small_font)
            cw = cb[2] - cb[0]
            min_y = b['min_y']
            draw.rectangle([cx - cw / 2 - 4, min_y - 25, cx + cw / 2 + 4, min_y - 5], fill=color, outline='white', width=1)
            draw.text((cx - cw / 2, min_y - 23), conf_text, fill='white', font=small_font)

        out_dir = original_path.parent / "output"
        out_dir.mkdir(exist_ok=True)
        out_path = out_dir / f"{original_path.stem}_enhanced_annotated{original_path.suffix}"
        img.save(out_path)
        return str(out_path)


# 如果你想单独测试：
if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser()
    parser.add_argument('--img', required=True, help='path to image')
    parser.add_argument('--prompt', default='请识别手写内容，只输出文字')
    args = parser.parse_args()

    ocr = ImprovedWebHandwritingOCR()
    ocr.update_parameters(prompt=args.prompt)
    res = ocr.run_with_visualization(args.img)
    print("\n===== FINAL TEXT =====\n")
    print(res['text'])
    print("\nAnnotated image saved at:", res['annotated_image'])
