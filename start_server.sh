#!/bin/bash

# AI Handwriting OCR Web Application Startup Script
# Author: Assistant
# Description: Quick startup script for the handwriting OCR web application

echo "🚀 Starting AI Handwriting OCR Web Application"
echo "=============================================="

# Check for version argument
VERSION=${1:-"improved"}

if [[ "$VERSION" == "improved" ]]; then
    APP_FILE="app_improved.py"
    echo "✨ Starting IMPROVED version with TextDetection + Qwen2.5-VL"
elif [[ "$VERSION" == "original" ]]; then
    APP_FILE="app.py"
    echo "📋 Starting ORIGINAL version"
else
    echo "❌ Invalid version. Use 'improved' or 'original'"
    echo "Usage: $0 [improved|original]"
    exit 1
fi

# Check if conda is available
if ! command -v conda &> /dev/null; then
    echo "❌ Error: Conda is not installed or not in PATH"
    echo "Please install Anaconda/Miniconda first"
    exit 1
fi

# Check if environment exists
ENV_NAME="handwriting-ocr"
if ! conda env list | grep -q "^${ENV_NAME}"; then
    echo "📦 Creating conda environment: ${ENV_NAME}"
    conda create -n ${ENV_NAME} python=3.9 -y
    
    echo "🔧 Installing PyTorch with CUDA support..."
    source $(conda info --base)/etc/profile.d/conda.sh
    conda activate ${ENV_NAME}
    conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia -y
    
    echo "📋 Installing Python dependencies..."
    pip install -r requirements.txt
    pip install shapely>=1.7.0
else
    echo "✅ Found existing environment: ${ENV_NAME}"
fi

# Activate environment
echo "🔄 Activating conda environment..."
source $(conda info --base)/etc/profile.d/conda.sh
conda activate ${ENV_NAME}

# Check if required directories exist
if [ ! -d "uploads" ]; then
    echo "📁 Creating uploads directory..."
    mkdir -p uploads
fi

if [ ! -d "output" ]; then
    echo "📁 Creating output directory..."
    mkdir -p output
fi

if [ ! -d "templates" ]; then
    echo "❌ Error: templates directory not found"
    echo "Please ensure you're running this script from the chain directory"
    exit 1
fi

# Check if required files exist
if [[ "$VERSION" == "improved" ]]; then
    REQUIRED_FILES=("app_improved.py" "web_ocr_improved.py" "chain.py" "templates/index_improved.html")
else
    REQUIRED_FILES=("app.py" "web_ocr.py" "chain.py" "templates/index.html")
fi

for file in "${REQUIRED_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        echo "❌ Error: Required file $file not found"
        exit 1
    fi
done

# Set environment variables
export FLASK_ENV=development
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# Get server IP for display
SERVER_IP=$(hostname -I | awk '{print $1}')

echo ""
echo "🌐 Server Information:"
echo "   Local access: http://localhost:5000"
echo "   Server IP: http://${SERVER_IP}:5000"
echo ""
echo "📖 Remote Access Options:"
echo "   1. SSH Tunnel: ssh -L 5000:localhost:5000 $(whoami)@${SERVER_IP}"
echo "   2. Direct access: http://${SERVER_IP}:5000 (if firewall allows)"
echo "   3. Use ngrok for public access"
echo ""

if [[ "$VERSION" == "improved" ]]; then
    echo "✨ IMPROVED VERSION FEATURES:"
    echo "   • TextDetection class for pure detection"
    echo "   • Qwen2.5-VL for pure recognition"
    echo "   • Smart reading order sorting"
    echo "   • Confidence-based color coding"
    echo "   • Detailed processing statistics"
    echo "   • 5-step visualization process"
fi

echo ""
echo "🛑 Press Ctrl+C to stop the server"
echo "=============================================="

# Start the Flask application
echo "🚀 Starting Flask server with ${APP_FILE}..."
python ${APP_FILE} 