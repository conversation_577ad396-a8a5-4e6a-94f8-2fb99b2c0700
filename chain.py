#!/usr/bin/env python3
# coding: utf-8

import time
from pathlib import Path
from typing import List, Dict

import numpy as np
from PIL import Image, UnidentifiedImageError
import torch
from paddleocr import PaddleOCR
from transformers import Qwen2_5_VLForConditionalGeneration, AutoProcessor
from qwen_vl_utils import process_vision_info

LOCAL_MODEL_DIR = (
    "/home/<USER>/HandWriting_Qwen2.5-VL/models/"
    "Qwen/Qwen2___5-VL-7B-Instruct"
)


class HandwritingOCR:
    def __init__(
        self,
        prompt: str = "Please recognize the handwriting and output only the text:",
        lang: str = "ch",
        det_thresh: float = 0.3,
        box_thresh: float = 0.6,
        overlap_threshold: float = 0.7
    ):
        # PaddleOCR detection on CPU with updated parameter names
        self.detector = PaddleOCR(
            device="cpu", 
            lang=lang,
            text_det_thresh=det_thresh,
            text_det_box_thresh=box_thresh,
            use_textline_orientation=True
        )

        # Qwen2.5-VL local
        self.model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
            LOCAL_MODEL_DIR,
            device_map="auto",
            torch_dtype="auto",
            trust_remote_code=True,
            local_files_only=True,
        ).eval()
        self.processor = AutoProcessor.from_pretrained(
            LOCAL_MODEL_DIR, trust_remote_code=True, local_files_only=True
        )
        self.prompt = prompt
        self.overlap_threshold = overlap_threshold
        print(f"[INIT] PaddleOCR -> CPU (lang={lang}, det_thresh={det_thresh}, box_thresh={box_thresh})")
        print(f"[INIT] Qwen2.5-VL ->", self.model.device)
        print(f"[INIT] Overlap threshold: {overlap_threshold:.1%}")

    # ---------- helper ----------
    @staticmethod
    def _crop(pil_img: Image.Image, box, pad: float = 0.05) -> Image.Image:
        pts = np.asarray(box)
        xmin, ymin = pts.min(axis=0)
        xmax, ymax = pts.max(axis=0)
        w, h = xmax - xmin, ymax - ymin
        return pil_img.crop(
            (
                max(0, int(xmin - w * pad)),
                max(0, int(ymin - h * pad)),
                min(pil_img.width, int(xmax + w * pad)),
                min(pil_img.height, int(ymax + h * pad)),
            )
        )

    def _recognize_line(self, img: Image.Image) -> str:
        messages = [
            {
                "role": "user",
                "content": [
                    {"type": "image", "image": img},
                    {"type": "text", "text": self.prompt},
                ],
            }
        ]
        text_prompt = self.processor.apply_chat_template(
            messages, tokenize=False, add_generation_prompt=True
        )
        img_inputs, vid_inputs = process_vision_info(messages)
        inputs = self.processor(
            text=[text_prompt],
            images=img_inputs,
            videos=vid_inputs,
            padding=True,
            return_tensors="pt",
        ).to(self.model.device)

        out_ids = self.model.generate(
            **inputs,
            max_new_tokens=8000,
            temperature=0.1,
            pad_token_id=self.processor.tokenizer.eos_token_id,
        )
        out_ids = out_ids[:, inputs.input_ids.shape[1] :]
        return self.processor.batch_decode(
            out_ids, skip_special_tokens=True, clean_up_tokenization_spaces=False
        )[0].strip()

    def _merge_overlapping_boxes(self, boxes: List[np.ndarray]) -> List[np.ndarray]:
        """
        合并重叠超过指定阈值的检测框
        """
        if not boxes or len(boxes) <= 1:
            return boxes
        
        print(f"[DEBUG] 开始合并重叠框，原始框数量：{len(boxes)}，重叠阈值：{self.overlap_threshold:.1%}")
        
        # 将框转换为便于处理的格式
        box_rects = []
        for i, box in enumerate(boxes):
            min_x = np.min(box[:, 0])
            max_x = np.max(box[:, 0])
            min_y = np.min(box[:, 1])
            max_y = np.max(box[:, 1])
            
            box_rects.append({
                'index': i,
                'box': box,
                'min_x': min_x,
                'max_x': max_x,
                'min_y': min_y,
                'max_y': max_y,
                'area': (max_x - min_x) * (max_y - min_y)
            })
        
        # 使用并查集算法进行分组
        merged_groups = []
        processed = set()
        
        for i, rect_i in enumerate(box_rects):
            if i in processed:
                continue
                
            # 创建新的组
            current_group = [rect_i]
            processed.add(i)
            
            # 查找与当前框重叠的其他框
            for j, rect_j in enumerate(box_rects):
                if j in processed or i == j:
                    continue
                
                # 检查是否重叠超过阈值
                if self._calculate_overlap_ratio(rect_i, rect_j) >= self.overlap_threshold:
                    current_group.append(rect_j)
                    processed.add(j)
                    
                    # 递归查找与新加入框重叠的框
                    self._find_overlapping_boxes_recursive(rect_j, box_rects, current_group, processed)
            
            merged_groups.append(current_group)
        
        # 为每个组创建合并后的框
        merged_boxes = []
        merge_count = 0
        
        for group in merged_groups:
            if len(group) == 1:
                # 单独的框，直接保留
                merged_boxes.append(group[0]['box'])
            else:
                # 多个框，合并成一个
                merged_box = self._create_merged_box_from_group(group)
                merged_boxes.append(merged_box)
                merge_count += len(group) - 1
        
        print(f"[DEBUG] 重叠框合并完成：{len(boxes)} -> {len(merged_boxes)}，合并了 {merge_count} 个重叠框")
        return merged_boxes
    
    def _find_overlapping_boxes_recursive(self, current_rect: Dict, all_rects: List[Dict], 
                                         current_group: List[Dict], processed: set):
        """
        递归查找与当前框重叠的所有框
        """
        for i, rect in enumerate(all_rects):
            if i in processed:
                continue
                
            if self._calculate_overlap_ratio(current_rect, rect) >= self.overlap_threshold:
                current_group.append(rect)
                processed.add(i)
                # 递归查找
                self._find_overlapping_boxes_recursive(rect, all_rects, current_group, processed)
    
    def _calculate_overlap_ratio(self, rect1: Dict, rect2: Dict) -> float:
        """
        计算两个矩形的重叠比例（相对于较小矩形的面积）
        """
        # 计算相交区域
        intersect_left = max(rect1['min_x'], rect2['min_x'])
        intersect_right = min(rect1['max_x'], rect2['max_x'])
        intersect_top = max(rect1['min_y'], rect2['min_y'])
        intersect_bottom = min(rect1['max_y'], rect2['max_y'])
        
        # 检查是否有相交
        if intersect_left >= intersect_right or intersect_top >= intersect_bottom:
            return 0.0
        
        # 计算相交面积
        intersect_area = (intersect_right - intersect_left) * (intersect_bottom - intersect_top)
        
        # 计算重叠比例（相对于较小的矩形）
        smaller_area = min(rect1['area'], rect2['area'])
        if smaller_area == 0:
            return 0.0
            
        overlap_ratio = intersect_area / smaller_area
        return overlap_ratio
    
    def _create_merged_box_from_group(self, group: List[Dict]) -> np.ndarray:
        """
        从一组重叠的框创建合并后的边界框
        """
        if not group:
            return np.array([])
        
        # 计算边界
        min_x = min(rect['min_x'] for rect in group)
        max_x = max(rect['max_x'] for rect in group)
        min_y = min(rect['min_y'] for rect in group)
        max_y = max(rect['max_y'] for rect in group)
        
        # 创建新的四点框（顺时针顺序）
        merged_box = np.array([
            [min_x, min_y],  # 左上
            [max_x, min_y],  # 右上
            [max_x, max_y],  # 右下
            [min_x, max_y]   # 左下
        ], dtype=np.float32)
        
        return merged_box

    # ---------- main ----------
    def run(self, image_path: str) -> Dict:
        img_path = Path(image_path)
        if not img_path.exists():
            raise FileNotFoundError(img_path)

        try:
            pil_img = Image.open(img_path).convert("RGB")
        except UnidentifiedImageError:
            raise ValueError("unsupported image format")

        start = time.time()

        # 1. detect text regions using the newer predict method
        print(f"[DEBUG] Processing image: {img_path}")
        print(f"[DEBUG] Image size: {pil_img.size}")
        
        try:
            detect_out = self.detector.predict(str(img_path))
        except AttributeError:
            # Fallback to older ocr method if predict doesn't exist
            detect_out = self.detector.ocr(str(img_path))
        
        print(f"[DEBUG] PaddleOCR output type: {type(detect_out)}")
        if isinstance(detect_out, list) and len(detect_out) > 0:
            print(f"[DEBUG] First element type: {type(detect_out[0])}")
            if hasattr(detect_out[0], 'get') or isinstance(detect_out[0], dict):
                if 'dt_polys' in detect_out[0]:
                    print(f"[DEBUG] Raw detection count: {len(detect_out[0]['dt_polys'])}")
                if 'rec_texts' in detect_out[0]:
                    print(f"[DEBUG] Raw recognition count: {len(detect_out[0]['rec_texts'])}")
                    print(f"[DEBUG] Sample texts: {detect_out[0]['rec_texts'][:3]}...")

        # unify to List[np.ndarray(4,2)] with robust handling of new PaddleOCR format
        boxes = []
        
        if hasattr(detect_out, "boxes"):
            boxes = [box for box in detect_out.boxes]
        elif isinstance(detect_out, list) and len(detect_out) > 0:
            # Handle new OCRResult format
            first_result = detect_out[0]
            
            # Check if it's the new OCRResult object format
            if hasattr(first_result, 'get') or isinstance(first_result, dict):
                # New format: extract from dt_polys field
                if 'dt_polys' in first_result:
                    print(f"[DEBUG] Found {len(first_result['dt_polys'])} detection boxes in new format")
                    boxes = first_result['dt_polys']
                elif hasattr(first_result, 'dt_polys'):
                    print(f"[DEBUG] Found {len(first_result.dt_polys)} detection boxes in new format")
                    boxes = first_result.dt_polys
                else:
                    print("[DEBUG] New format detected but no dt_polys found")
            # Handle old format if present
            elif hasattr(first_result, "boxes"):
                boxes = [box for box in first_result.boxes]
            else:
                # Try old list format
                boxes = []
                for item in detect_out:
                    try:
                        if (item is not None and 
                            isinstance(item, (list, tuple)) and 
                            len(item) > 0 and 
                            item[0] is not None):
                            boxes.append(np.asarray(item[0]))
                    except (IndexError, KeyError, TypeError, AttributeError):
                        continue
        else:
            boxes = []
        
        print(f"[DEBUG] Successfully extracted {len(boxes)} text boxes")
        
        # Debug: Check the format of extracted boxes
        if len(boxes) > 0:
            print(f"[DEBUG] First box type: {type(boxes[0])}")
            print(f"[DEBUG] First box shape: {boxes[0].shape if hasattr(boxes[0], 'shape') else 'No shape'}")
            print(f"[DEBUG] First box content: {boxes[0]}")

        if not boxes:
            print(f"[DEBUG] No valid boxes found. Total items processed: {len(detect_out) if isinstance(detect_out, list) else 'N/A'}")
            print("[SUGGESTION] Try these solutions:")
            print("1. Check if the image contains clear, readable text")
            print("2. Try with different language: lang='en' for English text")
            print("3. Adjust detection thresholds (lower det_db_thresh)")
            print("4. Ensure image has good contrast and quality")
            raise ValueError("no text detected")

        # 1.5. merge overlapping boxes (NEW FEATURE)
        print(f"[DEBUG] Merging overlapping boxes...")
        merged_boxes = self._merge_overlapping_boxes(boxes)

        # 2. sort boxes by y center
        print(f"[DEBUG] Sorting {len(merged_boxes)} boxes by Y coordinate...")
        merged_boxes.sort(key=lambda b: np.mean(b[:, 1]))

        # 3. crop + recognize with Qwen2.5-VL
        print(f"[DEBUG] Starting Qwen2.5-VL recognition for {len(merged_boxes)} text regions...")
        lines: List[str] = []
        for i, box in enumerate(merged_boxes):
            print(f"[DEBUG] Processing box {i+1}/{len(merged_boxes)}")
            try:
                crop = self._crop(pil_img, box)
                print(f"[DEBUG] Cropped image size: {crop.size}")
                recognized_text = self._recognize_line(crop)
                print(f"[DEBUG] Qwen2.5-VL result: '{recognized_text}'")
                lines.append(recognized_text)
            except Exception as e:
                print(f"[ERROR] Failed to process box {i+1}: {e}")
                lines.append(f"[ERROR: {str(e)}]")

        elapsed = time.time() - start
        return {"text": "\n".join(lines), "lines": lines, "time": elapsed}


# ---------- CLI ----------
if __name__ == "__main__":
    import argparse, textwrap

    ap = argparse.ArgumentParser(description="Handwriting OCR (PaddleOCR + Qwen2.5-VL)")
    ap.add_argument("image", help="path to handwriting image file")
    ap.add_argument("--lang", default="ch", help="language for PaddleOCR (ch/en/etc)")
    ap.add_argument("--det_thresh", type=float, default=0.3, help="detection threshold (lower = more sensitive)")
    ap.add_argument("--box_thresh", type=float, default=0.6, help="box threshold (lower = more boxes)")
    ap.add_argument("--overlap_threshold", type=float, default=0.7, help="overlap threshold for merging boxes (0.7 = 70%)")
    args = ap.parse_args()

    ocr = HandwritingOCR(
        lang=args.lang,
        det_thresh=args.det_thresh,
        box_thresh=args.box_thresh,
        overlap_threshold=args.overlap_threshold
    )
    result = ocr.run(args.image)

    print("\n========== OCR Result ==========\n")
    print(textwrap.indent(result["text"], "  "))
    print(f"\nLines: {len(result['lines'])}   Time: {result['time']:.2f}s")