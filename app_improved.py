#!/usr/bin/env python3
# coding: utf-8

import os
import time
from pathlib import Path
from werkzeug.utils import secure_filename
from flask import Flask, request, jsonify, render_template, send_file, url_for
import traceback

from web_ocr_improved import ImprovedWebHandwritingOCR

app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['OUTPUT_FOLDER'] = 'output'

# Create directories if they don't exist
Path(app.config['UPLOAD_FOLDER']).mkdir(exist_ok=True)
Path(app.config['OUTPUT_FOLDER']).mkdir(exist_ok=True)

# Global OCR instance (initialized on first use)
ocr_instance = None

ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'tiff', 'webp'}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def get_ocr_instance():
    """Get or create OCR instance"""
    global ocr_instance
    if ocr_instance is None:
        print("🚀 Initializing Improved OCR instance...")
        ocr_instance = ImprovedWebHandwritingOCR()
        print("✅ Improved OCR instance ready!")
    return ocr_instance

@app.route('/')
def index():
    """Main page"""
    return render_template('index_improved.html')

@app.route('/api/upload', methods=['POST'])
def upload_file():
    """Handle improved image upload and processing"""
    try:
        print("\n" + "="*60)
        print("🚀 IMPROVED OCR API REQUEST")
        print("="*60)
        
        # Check if file is in request
        if 'image' not in request.files:
            return jsonify({'success': False, 'error': 'No image file provided'})
        
        file = request.files['image']
        if file.filename == '':
            return jsonify({'success': False, 'error': 'No file selected'})
        
        if not allowed_file(file.filename):
            return jsonify({'success': False, 'error': 'Invalid file type'})
        
        # Get improved parameters
        params = {
            'language': request.form.get('language', 'ch'),
            'det_thresh': float(request.form.get('det_thresh', 0.3)),
            'box_thresh': float(request.form.get('box_thresh', 0.6)),
            'unclip_ratio': float(request.form.get('unclip_ratio', 2.0)),
            'image_size': int(request.form.get('image_size', 1280)),
            'prompt': request.form.get('prompt', 'Please recognize the handwriting and output only the text:'),
            'merge_threshold': float(request.form.get('merge_threshold', 0.07)),
            'min_concat_count': int(request.form.get('min_concat_count', 3)),
            'overlap_threshold': float(request.form.get('overlap_threshold', 0.7))
        }
        
        print(f"📊 Enhanced Parameters:")
        print(f"   Language: {params['language']}")
        print(f"   Detection Threshold: {params['det_thresh']}")
        print(f"   Box Threshold: {params['box_thresh']}")
        print(f"   Unclip Ratio: {params['unclip_ratio']}")
        print(f"   Image Size Limit: {params['image_size']}")
        print(f"   Merge Threshold: {params['merge_threshold']}")
        print(f"   Min Concat Count: {params['min_concat_count']}")
        print(f"   Overlap Threshold: {params['overlap_threshold']}")
        print(f"   Custom Prompt: {params['prompt'][:50]}...")
        
        # Save uploaded file
        filename = secure_filename(file.filename)
        timestamp = str(int(time.time() * 1000))
        unique_filename = f"{timestamp}_{filename}"
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)
        file.save(filepath)
        
        print(f"💾 File saved: {unique_filename}")
        print(f"📁 File path: {filepath}")
        
        # Get OCR instance (create if first time)
        ocr = get_ocr_instance()
        
        # Update OCR instance parameters dynamically
        print(f"🔧 Updating OCR parameters...")
        ocr.update_parameters(
            lang=params['language'],
            det_thresh=params['det_thresh'],
            box_thresh=params['box_thresh'],
            unclip_ratio=params['unclip_ratio'],
            image_size=params['image_size'],
            prompt=params['prompt'],
            merge_threshold=params['merge_threshold'],
            min_concat_count=params['min_concat_count'],
            overlap_threshold=params['overlap_threshold']
        )
        
        # Process image with improved OCR
        print(f"🔍 Starting enhanced TextDetection + Qwen2.5-VL processing...")
        result = ocr.run_with_visualization(filepath, custom_prompt=params['prompt'])
        
        # Prepare enhanced response
        response_data = {
            'success': True,
            'message': 'Enhanced processing completed successfully!',
            'text': result['text'],
            'lines': result['lines'],
            'boxes': result['boxes'],
            'time': result['time'],
            'total_boxes': result['total_boxes'],
            'image_size': result['image_size'],
            'annotated_image': result['annotated_image'].replace(os.getcwd() + '/', ''),
            'statistics': result['statistics'],
            'processing_steps': result['processing_steps'],
            'parameters_used': params
        }
        
        print(f"✅ Enhanced processing completed successfully!")
        print(f"📊 Results summary:")
        print(f"   Total regions: {result['total_boxes']}")
        print(f"   Success rate: {result['statistics']['success_rate']:.1f}%")
        print(f"   Average confidence: {result['statistics']['avg_confidence']:.3f}")
        print(f"   Processing time: {result['time']:.2f}s")
        print(f"   Detection time: {result['processing_steps']['detection_time']:.2f}s")
        print(f"   Recognition time: {result['processing_steps']['recognition_time']:.2f}s")
        
        return jsonify(response_data)
        
    except Exception as e:
        print(f"❌ Enhanced processing error: {str(e)}")
        traceback.print_exc()
        return jsonify({
            'success': False, 
            'error': f'Enhanced processing failed: {str(e)}'
        })

@app.route('/api/parameters', methods=['GET'])
def get_parameters():
    """Get current enhanced OCR parameters"""
    try:
        ocr = get_ocr_instance()
        return jsonify({
            'success': True,
            'parameters': {
                'language': ocr.lang,
                'det_thresh': ocr.det_thresh,
                'box_thresh': ocr.box_thresh,
                'unclip_ratio': ocr.unclip_ratio,
                'image_size': ocr.image_size,
                'prompt': ocr.prompt
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e),
            'default_parameters': {
                'language': 'ch',
                'det_thresh': 0.3,
                'box_thresh': 0.6,
                'unclip_ratio': 2.0,
                'image_size': 1280,
                'prompt': 'Please recognize the handwriting and output only the text:'
            }
        })

@app.route('/api/statistics/<int:processing_id>', methods=['GET'])
def get_detailed_statistics(processing_id):
    """Get detailed processing statistics for a specific result"""
    # This would be implemented if we stored results in a database
    # For now, statistics are returned with the main result
    return jsonify({'message': 'Statistics included in main result'})

@app.route('/uploads/<path:filename>')
def serve_upload(filename):
    """Serve uploaded files"""
    return send_file(Path(app.config['UPLOAD_FOLDER']) / filename)

@app.route('/output/<path:filename>')
def serve_output(filename):
    """Serve output files"""
    file_path = Path(app.config['OUTPUT_FOLDER']) / filename
    if not file_path.exists():
        # Try looking in chain/output directory
        file_path = Path('chain/output') / filename
    return send_file(file_path)

@app.route('/chain/output/<path:filename>')
def serve_chain_output(filename):
    """Serve files from chain/output directory"""
    return send_file(Path('chain/output') / filename)

@app.errorhandler(413)
def too_large(e):
    return jsonify({'error': 'File too large (max 16MB)'}), 413

@app.errorhandler(500)
def internal_error(e):
    return jsonify({'error': 'Internal server error'}), 500

if __name__ == '__main__':
    print("=" * 80)
    print("🚀 IMPROVED Handwriting OCR Web Application")
    print("=" * 80)
    print("✨ NEW FEATURES:")
    print("   • Smart reading order sorting")
    print("   • Confidence-based color coding")
    print("   • Detailed processing statistics")
    print("   • Enhanced visualization")
    print("   • Separated detection & recognition")
    print("=" * 80)
    print(f"📁 Upload folder: {Path(app.config['UPLOAD_FOLDER']).absolute()}")
    print(f"📁 Output folder: {Path(app.config['OUTPUT_FOLDER']).absolute()}")
    print("🌐 Starting improved server...")
    print("=" * 80)
    
    # Run the app
    app.run(host='0.0.0.0', port=5000, debug=True)