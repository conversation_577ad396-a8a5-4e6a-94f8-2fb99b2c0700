<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Improved Handwriting OCR - AI文字识别</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 30px;
        }

        .left-panel, .right-panel {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
        }

        .section-title {
            font-size: 1.3rem;
            color: #2c3e50;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        /* 文件上传区域 */
        .upload-area {
            border: 3px dashed #667eea;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            margin-bottom: 25px;
        }

        .upload-area:hover {
            background: #f0f4ff;
            border-color: #5a67d8;
        }

        .upload-area.drag-over {
            background: #e6f3ff;
            border-color: #4299e1;
        }

        .upload-icon {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 15px;
        }

        #imageInput {
            display: none;
        }

        /* 参数设置优化 */
        .parameters-section {
            margin-bottom: 25px;
        }

        .preset-modes {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin-bottom: 20px;
        }

        .preset-btn {
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-weight: 500;
        }

        .preset-btn:hover {
            border-color: #667eea;
            background: #f0f4ff;
        }

        .preset-btn.active {
            border-color: #667eea;
            background: #667eea;
            color: white;
        }

        .preset-info {
            font-size: 0.85rem;
            opacity: 0.8;
            margin-top: 4px;
        }

        .basic-params {
            margin-bottom: 20px;
        }

        .param-group {
            margin-bottom: 20px;
        }

        .param-label {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-weight: 500;
            color: #2d3748;
        }

        .param-value {
            background: #667eea;
            color: white;
            padding: 4px 10px;
            border-radius: 6px;
            font-size: 0.9rem;
            min-width: 40px;
            text-align: center;
        }

        .param-slider {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: #e2e8f0;
            outline: none;
            margin-bottom: 5px;
        }

        .param-slider::-webkit-slider-thumb {
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #667eea;
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0,0,0,0.2);
        }

        .param-description {
            font-size: 0.85rem;
            color: #718096;
            margin-bottom: 5px;
        }

        .advanced-toggle {
            display: flex;
            align-items: center;
            gap: 10px;
            cursor: pointer;
            padding: 12px;
            background: #e2e8f0;
            border-radius: 8px;
            margin: 15px 0;
            transition: all 0.3s ease;
        }

        .advanced-toggle:hover {
            background: #cbd5e0;
        }

        .advanced-params {
            display: none;
            margin-top: 15px;
            padding: 15px;
            background: #f7fafc;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .advanced-params.show {
            display: block;
        }

        select, textarea, .param-select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
            background: white;
        }

        select:focus, textarea:focus, .param-select:focus {
            outline: none;
            border-color: #667eea;
        }

        .param-select {
            cursor: pointer;
        }

        textarea {
            resize: vertical;
            min-height: 80px;
        }

        .reset-btn {
            background: #f56565;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: background 0.3s ease;
        }

        .reset-btn:hover {
            background: #e53e3e;
        }

        /* 处理按钮 */
        .process-btn {
            width: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .process-btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .process-btn:disabled {
            background: #a0aec0;
            cursor: not-allowed;
        }

        /* 加载动画 */
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #e2e8f0;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 处理步骤显示 */
        .processing-steps {
            display: none;
            margin: 20px 0;
        }

        .step {
            display: flex;
            align-items: center;
            padding: 10px;
            margin: 8px 0;
            background: #f7fafc;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .step.active {
            background: #e6f3ff;
            border-left: 4px solid #667eea;
        }

        .step.completed {
            background: #f0fff4;
            border-left: 4px solid #48bb78;
        }

        .step-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #e2e8f0;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .step.active .step-icon {
            background: #667eea;
            color: white;
        }

        .step.completed .step-icon {
            background: #48bb78;
            color: white;
        }

        /* 结果显示区域 */
        .results-section {
            margin-top: 25px;
        }

        .result-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .tab-btn {
            padding: 10px 20px;
            border: 2px solid #e2e8f0;
            background: white;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .tab-btn.active {
            border-color: #667eea;
            background: #667eea;
            color: white;
        }

        .tab-content {
            display: none;
            background: white;
            border-radius: 10px;
            padding: 20px;
            border: 2px solid #e2e8f0;
        }

        .tab-content.active {
            display: block;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: #f7fafc;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid #667eea;
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #718096;
        }

        .confidence-legend {
            display: flex;
            gap: 20px;
            margin: 15px 0;
            flex-wrap: wrap;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 3px;
        }

        .text-boxes {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
        }

        .text-box-item {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            padding: 10px;
            border-radius: 6px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .text-box-item:hover {
            background: #f7fafc;
        }

        .box-number {
            min-width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 0.9rem;
        }

        .box-content {
            flex: 1;
        }

        .box-text {
            margin-bottom: 5px;
            word-break: break-all;
        }

        .box-meta {
            font-size: 0.8rem;
            color: #718096;
        }

        .final-text {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            line-height: 1.6;
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .preset-modes {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .main-content {
                padding: 20px;
                gap: 20px;
            }
            
            .left-panel, .right-panel {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Improved Handwriting OCR</h1>
            <p>TextDetection + Qwen2.5-VL | 智能手写识别系统</p>
        </div>

        <div class="main-content">
            <!-- 左侧面板：上传和参数 -->
            <div class="left-panel">
                <!-- 文件上传 -->
                <div class="section-title">
                    📁 Image Upload
                </div>
                <div class="upload-area" onclick="document.getElementById('imageInput').click()">
                    <div class="upload-icon">📷</div>
                    <p style="font-size: 1.1rem; margin-bottom: 10px;">点击或拖拽上传图片</p>
                    <p style="color: #718096;">支持 JPG, PNG, GIF, BMP, TIFF, WebP (最大16MB)</p>
                </div>
                <input type="file" id="imageInput" accept="image/*">
                <div id="imagePreview" style="display: none; margin-top: 15px;">
                    <img id="previewImg" style="max-width: 100%; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1);">
                </div>

                <!-- 参数设置 -->
                <div class="section-title">
                    ⚙️ Smart Parameters
                </div>
                
                <!-- 预设模式 -->
                <div class="parameters-section">
                    <div style="font-weight: 500; margin-bottom: 10px; color: #2d3748;">🎯 Quick Presets</div>
                    <div class="preset-modes">
                        <div class="preset-btn active" data-preset="standard">
                            <div>📋 Standard</div>
                            <div class="preset-info">平衡模式</div>
                        </div>
                        <div class="preset-btn" data-preset="sensitive">
                            <div>🔍 Sensitive</div>
                            <div class="preset-info">高敏感度</div>
                        </div>
                        <div class="preset-btn" data-preset="precise">
                            <div>🎯 Precise</div>
                            <div class="preset-info">高精度</div>
                        </div>
                    </div>
                </div>

                <!-- 基础参数 -->
                <div class="basic-params">
                    <div class="param-group">
                        <label class="param-label">Language</label>
                        <select id="language">
                            <option value="ch">Chinese (中文)</option>
                            <option value="en">English</option>
                            <option value="french">French</option>
                            <option value="german">German</option>
                            <option value="korean">Korean</option>
                            <option value="japan">Japanese</option>
                        </select>
                    </div>

                    <div class="param-group">
                        <div class="param-label">
                            <span>Detection Sensitivity</span>
                            <span class="param-value" id="detectionValue">0.3</span>
                        </div>
                        <div class="param-description">📊 Higher = fewer regions, Lower = more regions</div>
                        <input type="range" id="detectionThreshold" class="param-slider" 
                               min="0.1" max="0.8" step="0.1" value="0.3">
                    </div>

                    <div class="param-group">
                        <div class="param-label">
                            <span>Box Quality</span>
                            <span class="param-value" id="boxValue">0.6</span>
                        </div>
                        <div class="param-description">📦 Higher = stricter quality, Lower = more boxes</div>
                        <input type="range" id="boxThreshold" class="param-slider" 
                               min="0.3" max="0.9" step="0.1" value="0.6">
                    </div>
                </div>

                <!-- 高级选项 -->
                <div class="advanced-toggle" onclick="toggleAdvanced()">
                    <span>🔧 Advanced Options</span>
                    <span id="advancedIcon">▼</span>
                </div>
                <div class="advanced-params" id="advancedParams">
                    <div class="param-group">
                        <div class="param-label">
                            <span>Unclip Ratio</span>
                            <span class="param-value" id="unclipValue">2.0</span>
                        </div>
                        <div class="param-description">🔧 Text box expansion ratio (1.5-3.0)</div>
                        <input type="range" id="unclipRatio" class="param-slider" 
                               min="1.5" max="3.0" step="0.1" value="2.0">
                    </div>

                    <div class="param-group">
                        <div class="param-label">
                            <span>Image Size Limit</span>
                            <span class="param-value" id="imageSizeValue">1280</span>
                        </div>
                        <div class="param-description">📏 Maximum image dimension for processing</div>
                        <input type="range" id="imageSize" class="param-slider" 
                               min="960" max="1920" step="80" value="1280">
                    </div>

                    <div class="param-group">
                        <label class="param-label">Text Merge Threshold</label>
                        <div class="param-description">🔗 Controls how text regions are merged vertically</div>
                        <div style="display: flex; gap: 10px; align-items: center;">
                            <select id="mergeThreshold" class="param-select" style="flex: 1;">
                                <option value="0.05">Fine (0.05) - More separate regions</option>
                                <option value="0.07" selected>Standard (0.07) - Balanced merging</option>
                                <option value="0.5">Loose (0.5) - Fewer, larger regions</option>
                            </select>
                            <input type="number" id="mergeThresholdCustom" class="param-select" 
                                   style="width: 80px;" min="0.01" max="1.0" step="0.01" 
                                   placeholder="Custom" title="Custom merge threshold value">
                        </div>
                    </div>

                    <div class="param-group">
                        <label class="param-label">Minimum Concatenation Count</label>
                        <div class="param-description">📝 Minimum number of text boxes to merge in each column</div>
                        <select id="minConcatCount" class="param-select">
                            <option value="1">1 - No forced merging</option>
                            <option value="2">2 - Merge at least 2 boxes</option>
                            <option value="3" selected>3 - Merge at least 3 boxes</option>
                            <option value="4">4 - Merge at least 4 boxes</option>
                            <option value="5">5 - Merge at least 5 boxes</option>
                            <option value="10">10 - Aggressive merging</option>
                        </select>
                    </div>

                    <div class="param-group">
                        <label class="param-label">Overlap Merge Threshold</label>
                        <div class="param-description">🔄 Merge overlapping boxes when overlap > threshold (70% recommended)</div>
                        <div style="display: flex; gap: 10px; align-items: center;">
                            <input type="range" id="overlapThreshold" class="param-slider" 
                                   min="0.5" max="0.9" step="0.05" value="0.7" style="flex: 1;">
                            <span class="param-value" id="overlapThresholdValue">0.7</span>
                        </div>
                        <div style="font-size: 0.8rem; color: #718096; margin-top: 5px;">
                            Higher values = more strict overlap requirement for merging
                        </div>
                    </div>

                    <div class="param-group">
                        <label class="param-label">Custom Recognition Prompt</label>
                        <textarea id="customPrompt" placeholder="Enter custom recognition instructions...">提取作文内容，只输出图片内容，不要输出任何其他多余的内容</textarea>
                    </div>

                    <button class="reset-btn" onclick="resetToDefaults()">
                        🔄 Reset to Defaults
                    </button>
                </div>

                <!-- 处理按钮 -->
                <button id="processBtn" class="process-btn" onclick="processImage()">
                    🚀 Start Recognition
                </button>

                <!-- 加载动画 -->
                <div id="loading" class="loading">
                    <div class="spinner"></div>
                    <p>AI is analyzing your image...</p>
                </div>

                <!-- 处理步骤 -->
                <div id="processingSteps" class="processing-steps">
                    <div class="step" id="step1">
                        <div class="step-icon">1</div>
                        <div>TextDetection Analysis</div>
                    </div>
                    <div class="step" id="step2">
                        <div class="step-icon">2</div>
                        <div>Smart Column Sorting</div>
                    </div>
                    <div class="step" id="step3">
                        <div class="step-icon">3</div>
                        <div>Vertical Region Merging</div>
                    </div>
                    <div class="step" id="step4">
                        <div class="step-icon">4</div>
                        <div>Smart Region Cropping</div>
                    </div>
                    <div class="step" id="step5">
                        <div class="step-icon">5</div>
                        <div>Qwen2.5-VL Recognition</div>
                    </div>
                    <div class="step" id="step6">
                        <div class="step-icon">6</div>
                        <div>Enhanced Visualization</div>
                    </div>
                </div>
            </div>

            <!-- 右侧面板：结果显示 -->
            <div class="right-panel">
                <div class="section-title">
                    📊 Recognition Results
                </div>

                <div id="results" style="display: none;">
                    <!-- 结果标签页 -->
                    <div class="result-tabs">
                        <div class="tab-btn active" onclick="switchTab('overview')">📊 Overview</div>
                        <div class="tab-btn" onclick="switchTab('details')">📝 Details</div>
                        <div class="tab-btn" onclick="switchTab('text')">📋 Final Text</div>
                    </div>

                    <!-- 概览标签页 -->
                    <div id="overview" class="tab-content active">
                        <div id="statsContainer" class="stats-grid">
                            <!-- 统计卡片将在这里动态生成 -->
                        </div>

                        <div class="confidence-legend">
                            <div class="legend-item">
                                <div class="legend-color" style="background: #22c55e;"></div>
                                <span>High Confidence (≥0.8)</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color" style="background: #f59e0b;"></div>
                                <span>Medium Confidence (0.5-0.8)</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color" style="background: #ef4444;"></div>
                                <span>Low Confidence (<0.5)</span>
                            </div>
                        </div>

                        <div id="annotatedImageContainer" style="text-align: center;">
                            <!-- 标注图片将在这里显示 -->
                        </div>
                    </div>

                    <!-- 详情标签页 -->
                    <div id="details" class="tab-content">
                        <div id="textBoxes" class="text-boxes">
                            <!-- 文本框详情将在这里显示 -->
                        </div>
                    </div>

                    <!-- 最终文本标签页 -->
                    <div id="text" class="tab-content">
                        <div id="finalText" class="final-text">
                            <!-- 最终识别文本将在这里显示 -->
                        </div>
                    </div>
                </div>

                <div id="noResults" style="text-align: center; color: #718096; margin-top: 50px;">
                    <div style="font-size: 3rem; margin-bottom: 15px;">🤖</div>
                    <p>Upload an image and click "Start Recognition" to see results</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentImageFile = null;
        let processedResults = null;

        // 预设参数配置
        const presets = {
            standard: {
                detectionThreshold: 0.3,
                boxThreshold: 0.6,
                unclipRatio: 2.0,
                imageSize: 1280,
                overlapThreshold: 0.7
            },
            sensitive: {
                detectionThreshold: 0.2,
                boxThreshold: 0.5,
                unclipRatio: 2.2,
                imageSize: 1400,
                overlapThreshold: 0.65
            },
            precise: {
                detectionThreshold: 0.4,
                boxThreshold: 0.7,
                unclipRatio: 1.8,
                imageSize: 1600,
                overlapThreshold: 0.75
            }
        };

        // 初始化事件监听器
        document.addEventListener('DOMContentLoaded', function() {
            initializeEventListeners();
            updateParameterValues();
        });

        function initializeEventListeners() {
            // 文件上传
            const imageInput = document.getElementById('imageInput');
            const uploadArea = document.querySelector('.upload-area');

            imageInput.addEventListener('change', handleImageUpload);
            
            // 拖拽上传
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('drag-over');
            });
            
            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('drag-over');
            });
            
            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('drag-over');
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    imageInput.files = files;
                    handleImageUpload();
                }
            });

            // 预设模式
            document.querySelectorAll('.preset-btn').forEach(btn => {
                btn.addEventListener('click', () => applyPreset(btn.dataset.preset));
            });

            // 参数滑块
            document.getElementById('detectionThreshold').addEventListener('input', updateParameterValues);
            document.getElementById('boxThreshold').addEventListener('input', updateParameterValues);
            document.getElementById('unclipRatio').addEventListener('input', updateParameterValues);
            document.getElementById('imageSize').addEventListener('input', updateParameterValues);
            document.getElementById('overlapThreshold').addEventListener('input', updateParameterValues);
        }

        function handleImageUpload() {
            const imageInput = document.getElementById('imageInput');
            const file = imageInput.files[0];
            
            if (file) {
                if (file.size > 16 * 1024 * 1024) {
                    alert('文件大小超过16MB限制！');
                    return;
                }
                
                currentImageFile = file;
                
                // 显示预览
                const reader = new FileReader();
                reader.onload = function(e) {
                    const previewImg = document.getElementById('previewImg');
                    const imagePreview = document.getElementById('imagePreview');
                    previewImg.src = e.target.result;
                    imagePreview.style.display = 'block';
                };
                reader.readAsDataURL(file);
                
                // 启用处理按钮
                document.getElementById('processBtn').disabled = false;
            }
        }

        function applyPreset(presetName) {
            // 更新按钮状态
            document.querySelectorAll('.preset-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-preset="${presetName}"]`).classList.add('active');

            // 应用预设参数
            const preset = presets[presetName];
            document.getElementById('detectionThreshold').value = preset.detectionThreshold;
            document.getElementById('boxThreshold').value = preset.boxThreshold;
            document.getElementById('unclipRatio').value = preset.unclipRatio;
            document.getElementById('imageSize').value = preset.imageSize;
            document.getElementById('overlapThreshold').value = preset.overlapThreshold;

            updateParameterValues();
        }

        function updateParameterValues() {
            document.getElementById('detectionValue').textContent = 
                document.getElementById('detectionThreshold').value;
            document.getElementById('boxValue').textContent = 
                document.getElementById('boxThreshold').value;
            document.getElementById('unclipValue').textContent = 
                document.getElementById('unclipRatio').value;
            document.getElementById('imageSizeValue').textContent = 
                document.getElementById('imageSize').value;
            document.getElementById('overlapThresholdValue').textContent = 
                document.getElementById('overlapThreshold').value;
        }

        function toggleAdvanced() {
            const params = document.getElementById('advancedParams');
            const icon = document.getElementById('advancedIcon');
            
            if (params.classList.contains('show')) {
                params.classList.remove('show');
                icon.textContent = '▼';
            } else {
                params.classList.add('show');
                icon.textContent = '▲';
            }
        }

        function resetToDefaults() {
            applyPreset('standard');
            document.getElementById('language').value = 'ch';
            document.getElementById('customPrompt').value = '提取作文内容，只输出图片内容，不要输出任何其他多余的内容';
        }

        function processImage() {
            if (!currentImageFile) {
                alert('请先上传图片！');
                return;
            }

            // 显示加载状态
            showLoading();
            
            // 准备表单数据
            const formData = new FormData();
            formData.append('image', currentImageFile);
            formData.append('language', document.getElementById('language').value);
            formData.append('det_thresh', document.getElementById('detectionThreshold').value);
            formData.append('box_thresh', document.getElementById('boxThreshold').value);
            formData.append('unclip_ratio', document.getElementById('unclipRatio').value);
            formData.append('image_size', document.getElementById('imageSize').value);
            
            // 处理merge_threshold：优先使用自定义输入值
            const customMergeThreshold = document.getElementById('mergeThresholdCustom').value;
            const mergeThresholdValue = customMergeThreshold ? customMergeThreshold : document.getElementById('mergeThreshold').value;
            formData.append('merge_threshold', mergeThresholdValue);
            
            // 添加min_concat_count参数
            formData.append('min_concat_count', document.getElementById('minConcatCount').value);
            
            // 添加overlap_threshold参数
            formData.append('overlap_threshold', document.getElementById('overlapThreshold').value);
            
            formData.append('prompt', document.getElementById('customPrompt').value);

            // 发送请求
            fetch('/api/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                if (data.success) {
                    processedResults = data;
                    displayResults(data);
                } else {
                    alert('处理失败：' + data.error);
                }
            })
            .catch(error => {
                hideLoading();
                console.error('Error:', error);
                alert('处理过程中发生错误！');
            });
        }

        function showLoading() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('processingSteps').style.display = 'block';
            document.getElementById('processBtn').disabled = true;
            
            // 模拟步骤进度
            simulateProcessingSteps();
        }

        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
            document.getElementById('processingSteps').style.display = 'none';
            document.getElementById('processBtn').disabled = false;
            
            // 重置步骤状态
            document.querySelectorAll('.step').forEach(step => {
                step.classList.remove('active', 'completed');
            });
        }

        function simulateProcessingSteps() {
            const steps = ['step1', 'step2', 'step3', 'step4', 'step5'];
            let currentStep = 0;
            
            const interval = setInterval(() => {
                if (currentStep > 0) {
                    document.getElementById(steps[currentStep - 1]).classList.remove('active');
                    document.getElementById(steps[currentStep - 1]).classList.add('completed');
                }
                
                if (currentStep < steps.length) {
                    document.getElementById(steps[currentStep]).classList.add('active');
                    currentStep++;
                } else {
                    clearInterval(interval);
                }
            }, 800);
        }

        function displayResults(data) {
            document.getElementById('noResults').style.display = 'none';
            document.getElementById('results').style.display = 'block';
            
            // 显示统计信息
            displayStatistics(data.statistics);
            
            // 显示标注图片
            displayAnnotatedImage(data.annotated_image);
            
            // 显示文本框详情
            displayTextBoxes(data.boxes);
            
            // 显示最终文本
            displayFinalText(data.text);
        }

        function displayStatistics(stats) {
            const container = document.getElementById('statsContainer');
            container.innerHTML = `
                <div class="stat-card">
                    <div class="stat-value">${stats.total_regions}</div>
                    <div class="stat-label">Total Regions</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${stats.successful_recognitions}</div>
                    <div class="stat-label">Successful</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${stats.success_rate.toFixed(1)}%</div>
                    <div class="stat-label">Success Rate</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${stats.avg_confidence.toFixed(3)}</div>
                    <div class="stat-label">Avg Confidence</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${stats.confidence_distribution.high}</div>
                    <div class="stat-label">High Confidence</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${stats.confidence_distribution.medium}</div>
                    <div class="stat-label">Medium Confidence</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${stats.confidence_distribution.low}</div>
                    <div class="stat-label">Low Confidence</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${stats.timing.total.toFixed(2)}s</div>
                    <div class="stat-label">Total Time</div>
                </div>
            `;
        }

        function displayAnnotatedImage(imagePath) {
            const container = document.getElementById('annotatedImageContainer');
            container.innerHTML = `
                <h4 style="margin-bottom: 15px; color: #2d3748;">📝 Enhanced Annotation</h4>
                <img src="${imagePath}" style="max-width: 100%; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1);">
            `;
        }

        function displayTextBoxes(boxes) {
            const container = document.getElementById('textBoxes');
            container.innerHTML = boxes.map(box => `
                <div class="text-box-item" onclick="highlightBox(${box.id})">
                    <div class="box-number" style="background: ${box.color};">
                        ${box.id}
                    </div>
                    <div class="box-content">
                        <div class="box-text">${box.text}</div>
                        <div class="box-meta">
                            Confidence: ${box.confidence.toFixed(3)} | 
                            Time: ${box.processing_time.toFixed(2)}s |
                            Status: ${box.status}
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function displayFinalText(text) {
            document.getElementById('finalText').textContent = text;
        }

        function switchTab(tabName) {
            // 更新标签按钮
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // 更新内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            document.getElementById(tabName).classList.add('active');
        }

        function highlightBox(boxId) {
            // 这里可以添加高亮显示功能
            console.log('Highlighting box:', boxId);
        }
    </script>
</body>
</html>