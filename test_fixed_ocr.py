#!/usr/bin/env python3
# coding: utf-8

"""
Test script for the fixed OCR system
"""

import sys
import os
from PIL import Image

# Add the chain directory to the path
sys.path.append('/home/<USER>/chain')

from web_ocr_improved import ImprovedWebHandwritingOCR

def test_ocr_fix():
    """Test the fixed OCR system"""
    try:
        print("=== Testing Fixed OCR System ===")
        
        # Initialize OCR (this tests if the processor initialization is correct)
        print("[1] Initializing OCR...")
        ocr = ImprovedWebHandwritingOCR()
        print("   ✅ OCR initialization successful")
        
        # Test with a sample image if available
        test_image_path = "/home/<USER>/chain/composition/2025050002-s-1.jpg"
        if os.path.exists(test_image_path):
            print(f"[2] Testing with image: {test_image_path}")
            
            # Process the image
            result = ocr.process_image(test_image_path, detailed_output=True)
            
            print("   ✅ Image processing successful")
            print(f"   📝 Result keys: {list(result.keys())}")
            
            if 'results' in result:
                print(f"   📊 Found {len(result['results'])} text regions")
                for i, res in enumerate(result['results'][:3]):  # Show first 3 results
                    print(f"      Region {i+1}: '{res.get('text', 'N/A')}' (conf: {res.get('confidence', 0):.3f})")
        else:
            print(f"[2] Test image not found: {test_image_path}")
            print("   ⚠️  Skipping image processing test")
            
        print("\n🎉 All tests completed successfully!")
        print("✅ The expand() tensor error has been fixed!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = test_ocr_fix()
    sys.exit(0 if success else 1)
