<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Handwriting OCR - AI手写识别系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 30px;
        }

        .upload-section, .result-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .section-title {
            font-size: 1.4rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: #1f2937;
            border-bottom: 3px solid #4f46e5;
            padding-bottom: 10px;
        }

        .upload-area {
            border: 3px dashed #d1d5db;
            border-radius: 12px;
            padding: 40px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            background: #f9fafb;
        }

        .upload-area:hover {
            border-color: #4f46e5;
            background: #f3f4f6;
        }

        .upload-area.dragover {
            border-color: #4f46e5;
            background: #eef2ff;
        }

        .upload-icon {
            font-size: 3rem;
            color: #6b7280;
            margin-bottom: 15px;
        }

        .upload-text {
            font-size: 1.1rem;
            color: #4b5563;
            margin-bottom: 15px;
        }

        .file-input {
            display: none;
        }

        .btn {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 70, 229, 0.4);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .parameters {
            margin-top: 25px;
            padding: 20px;
            background: #f8fafc;
            border-radius: 10px;
        }

        .param-group {
            margin-bottom: 20px;
        }

        .param-label {
            display: block;
            font-weight: 600;
            margin-bottom: 8px;
            color: #374151;
        }

        .param-input {
            width: 100%;
            padding: 10px;
            border: 2px solid #e5e7eb;
            border-radius: 6px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .param-input:focus {
            outline: none;
            border-color: #4f46e5;
        }

        .range-container {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .range-input {
            flex: 1;
        }

        .range-value {
            font-weight: 600;
            color: #4f46e5;
            min-width: 40px;
        }

        .image-display {
            max-width: 100%;
            height: auto;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            cursor: pointer;
        }

        .text-boxes {
            display: grid;
            gap: 15px;
            max-height: 400px;
            overflow-y: auto;
        }

        .text-box {
            padding: 15px;
            background: #f8fafc;
            border-radius: 8px;
            border-left: 4px solid #4f46e5;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .text-box:hover {
            background: #e0e7ff;
            transform: translateX(5px);
        }

        .text-box.active {
            background: #eef2ff;
            border-left-color: #f59e0b;
        }

        .box-id {
            font-weight: 700;
            color: #4f46e5;
            margin-bottom: 5px;
        }

        .box-text {
            color: #374151;
            line-height: 1.5;
        }

        .overall-result {
            margin-top: 20px;
            padding: 20px;
            background: #f0fdf4;
            border: 2px solid #22c55e;
            border-radius: 10px;
        }

        .overall-result h3 {
            color: #16a34a;
            margin-bottom: 15px;
        }

        .overall-text {
            color: #374151;
            line-height: 1.6;
            font-size: 1rem;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 4px solid #f3f4f6;
            border-top: 4px solid #4f46e5;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .status-info {
            margin-top: 15px;
            padding: 15px;
            background: #e0f2fe;
            border-radius: 8px;
            font-size: 0.9rem;
            color: #0369a1;
        }

        .error {
            background: #fef2f2;
            color: #dc2626;
            border: 2px solid #fecaca;
        }

        .success {
            background: #f0fdf4;
            color: #16a34a;
            border: 2px solid #bbf7d0;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 AI Handwriting OCR</h1>
            <p>Advanced handwriting recognition powered by PaddleOCR + Qwen2.5-VL</p>
        </div>

        <div class="main-content">
            <!-- Upload Section -->
            <div class="upload-section">
                <h2 class="section-title">📤 Upload & Process</h2>
                
                <div class="upload-area" id="uploadArea">
                    <div class="upload-icon">📄</div>
                    <div class="upload-text">Click to select image or drag & drop here</div>
                    <div style="font-size: 0.9rem; color: #9ca3af;">Support: JPG, PNG, GIF, BMP, TIFF, WebP (Max 16MB)</div>
                </div>
                
                <input type="file" id="fileInput" class="file-input" accept="image/*">
                
                <div class="parameters">
                    <h3 style="margin-bottom: 15px; color: #374151;">⚙️ Recognition Parameters</h3>
                    
                    <div class="param-group">
                        <label class="param-label">Language</label>
                        <select id="languageSelect" class="param-input">
                            <option value="ch">Chinese (中文)</option>
                            <option value="en">English</option>
                            <option value="fr">French</option>
                            <option value="german">German</option>
                            <option value="korean">Korean</option>
                            <option value="japan">Japanese</option>
                        </select>
                    </div>
                    
                    <div class="param-group">
                        <label class="param-label">Detection Threshold</label>
                        <div class="range-container">
                            <input type="range" id="detThresh" class="param-input range-input" 
                                   min="0.1" max="0.9" step="0.1" value="0.3">
                            <span class="range-value" id="detThreshValue">0.3</span>
                        </div>
                        <div style="font-size: 0.8rem; color: #6b7280; margin-top: 5px;">
                            Lower values detect more text regions
                        </div>
                    </div>
                    
                    <div class="param-group">
                        <label class="param-label">Box Threshold</label>
                        <div class="range-container">
                            <input type="range" id="boxThresh" class="param-input range-input" 
                                   min="0.1" max="0.9" step="0.1" value="0.6">
                            <span class="range-value" id="boxThreshValue">0.6</span>
                        </div>
                        <div style="font-size: 0.8rem; color: #6b7280; margin-top: 5px;">
                            Lower values include more potential text boxes
                        </div>
                    </div>
                    
                    <div class="param-group">
                        <label class="param-label">Custom Prompt (Optional)</label>
                        <textarea id="customPrompt" class="param-input" rows="3" 
                                  placeholder="Enter custom recognition prompt..."></textarea>
                    </div>
                    
                    <button id="processBtn" class="btn" disabled>
                        <span>🔍</span> Start Recognition
                    </button>
                </div>
                
                <div class="loading" id="loading">
                    <div class="spinner"></div>
                    <div>Processing your image...</div>
                    <div style="font-size: 0.9rem; color: #6b7280; margin-top: 10px;">
                        This may take a few moments depending on image complexity
                    </div>
                </div>
            </div>

            <!-- Result Section -->
            <div class="result-section">
                <h2 class="section-title">📊 Recognition Results</h2>
                
                <div id="resultContent" style="display: none;">
                    <div id="imageContainer">
                        <img id="annotatedImage" class="image-display" alt="Annotated Image">
                    </div>
                    
                    <div class="status-info" id="statusInfo"></div>
                    
                    <h3 style="margin: 20px 0 15px 0; color: #374151;">📝 Text Regions (Click to highlight)</h3>
                    <div id="textBoxes" class="text-boxes"></div>
                    
                    <div id="overallResult" class="overall-result" style="display: none;">
                        <h3>📋 Complete Recognition Result</h3>
                        <div id="overallText" class="overall-text"></div>
                    </div>
                </div>
                
                <div id="noResult" style="text-align: center; color: #9ca3af; padding: 40px;">
                    <div style="font-size: 2rem; margin-bottom: 15px;">🎯</div>
                    <div>Upload an image to start recognition</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let currentBoxes = [];
        let currentResult = null;

        // DOM elements
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');
        const processBtn = document.getElementById('processBtn');
        const loading = document.getElementById('loading');
        const resultContent = document.getElementById('resultContent');
        const noResult = document.getElementById('noResult');
        const detThresh = document.getElementById('detThresh');
        const boxThresh = document.getElementById('boxThresh');
        const detThreshValue = document.getElementById('detThreshValue');
        const boxThreshValue = document.getElementById('boxThreshValue');

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            setupEventListeners();
            updateRangeValues();
        });

        function setupEventListeners() {
            // File upload
            uploadArea.addEventListener('click', () => fileInput.click());
            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('dragleave', handleDragLeave);
            uploadArea.addEventListener('drop', handleDrop);
            fileInput.addEventListener('change', handleFileSelect);

            // Process button
            processBtn.addEventListener('click', processImage);

            // Range inputs
            detThresh.addEventListener('input', updateRangeValues);
            boxThresh.addEventListener('input', updateRangeValues);
        }

        function handleDragOver(e) {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        }

        function handleDragLeave(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        }

        function handleDrop(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                handleFileSelect();
            }
        }

        function handleFileSelect() {
            const file = fileInput.files[0];
            if (file) {
                processBtn.disabled = false;
                updateUploadArea(file.name);
            }
        }

        function updateUploadArea(filename) {
            uploadArea.innerHTML = `
                <div class="upload-icon">✅</div>
                <div class="upload-text">Selected: ${filename}</div>
                <div style="font-size: 0.9rem; color: #9ca3af;">Click to change file</div>
            `;
        }

        function updateRangeValues() {
            detThreshValue.textContent = detThresh.value;
            boxThreshValue.textContent = boxThresh.value;
        }

        async function processImage() {
            const file = fileInput.files[0];
            if (!file) return;

            // Show loading
            loading.style.display = 'block';
            processBtn.disabled = true;
            resultContent.style.display = 'none';
            noResult.style.display = 'none';

            const formData = new FormData();
            formData.append('file', file);
            formData.append('lang', document.getElementById('languageSelect').value);
            formData.append('det_thresh', detThresh.value);
            formData.append('box_thresh', boxThresh.value);
            formData.append('prompt', document.getElementById('customPrompt').value);

            try {
                const response = await fetch('/api/upload', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    displayResults(data.result);
                } else {
                    showError(data.error || 'Processing failed');
                }
            } catch (error) {
                showError('Network error: ' + error.message);
            } finally {
                loading.style.display = 'none';
                processBtn.disabled = false;
            }
        }

        function displayResults(result) {
            currentResult = result;
            currentBoxes = result.boxes || [];

            // Show annotated image
            const annotatedImage = document.getElementById('annotatedImage');
            annotatedImage.src = '/' + result.annotated_image;

            // Update status info
            const statusInfo = document.getElementById('statusInfo');
            statusInfo.className = 'status-info success';
            statusInfo.innerHTML = `
                ✅ Processing completed successfully!<br>
                📊 Detected ${result.total_boxes} text regions in ${result.time.toFixed(2)}s<br>
                📏 Image size: ${result.image_size[0]} × ${result.image_size[1]} pixels
            `;

            // Display text boxes
            displayTextBoxes(currentBoxes);

            // Display overall result
            displayOverallResult(result.text);

            // Show result section
            resultContent.style.display = 'block';
            noResult.style.display = 'none';
        }

        function displayTextBoxes(boxes) {
            const textBoxes = document.getElementById('textBoxes');
            textBoxes.innerHTML = '';

            boxes.forEach((box, index) => {
                const boxElement = document.createElement('div');
                boxElement.className = 'text-box';
                boxElement.innerHTML = `
                    <div class="box-id">Region ${box.id}</div>
                    <div class="box-text">${box.text || 'No text detected'}</div>
                `;

                boxElement.addEventListener('click', () => {
                    // Remove active class from all boxes
                    document.querySelectorAll('.text-box').forEach(el => el.classList.remove('active'));
                    // Add active class to clicked box
                    boxElement.classList.add('active');
                    
                    // You could add image highlighting logic here
                    console.log('Selected box:', box);
                });

                textBoxes.appendChild(boxElement);
            });
        }

        function displayOverallResult(text) {
            const overallResult = document.getElementById('overallResult');
            const overallText = document.getElementById('overallText');
            
            overallText.textContent = text || 'No text was recognized';
            overallResult.style.display = 'block';
        }

        function showError(message) {
            const statusInfo = document.getElementById('statusInfo');
            statusInfo.className = 'status-info error';
            statusInfo.innerHTML = `❌ Error: ${message}`;
            
            resultContent.style.display = 'block';
            noResult.style.display = 'none';
            
            // Hide other result elements
            document.getElementById('imageContainer').style.display = 'none';
            document.getElementById('textBoxes').style.display = 'none';
            document.getElementById('overallResult').style.display = 'none';
        }
    </script>
</body>
</html> 