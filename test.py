#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import argparse
import torch
from PIL import Image
from transformers import AutoProcessor, Qwen2_5_VLForConditionalGeneration
from qwen_vl_utils import process_vision_info

# ====== 根据你本地路径改这里 ======
LOCAL_MODEL_DIR = "/home/<USER>/HandWriting_Qwen2.5-VL/models/Qwen/Qwen2___5-VL-7B-Instruct"
# =================================

def dump_shapes(tag, d):
    print(f"\n[DBG] === {tag} ===")
    for k, v in d.items():
        if torch.is_tensor(v):
            print(f"[DBG] {k}: {tuple(v.shape)} {v.dtype}")
        else:
            print(f"[DBG] {k}: {type(v)}")
    print("[DBG] ===============\n")

def clean_inputs(inputs):
    out = {}
    for k, v in inputs.items():
        if isinstance(v, list):
            if len(v) == 0:
                continue
            if torch.is_tensor(v[0]):
                v = torch.stack(v, dim=0)
        out[k] = v
    return out

def build_inputs(messages, processor, device, max_length=8192):
    # 1) 模板 -> 文本
    text = processor.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)
    # 2) 视觉信息
    img_inputs, vid_inputs = process_vision_info(messages)
    # 3) 统一打包
    inputs = processor(
        text=[text],
        images=img_inputs,
        videos=vid_inputs,
        padding=True,
        truncation=True,
        max_length=max_length,
        return_tensors="pt",
    )
    inputs = clean_inputs(inputs)
    inputs = {k: v.to(device) if torch.is_tensor(v) else v for k, v in inputs.items()}
    return inputs

def main():
    ap = argparse.ArgumentParser()
    ap.add_argument("image", help="要测试的图片路径（单张）")
    ap.add_argument("--prompt", default="请识别手写内容，只输出文字：")
    ap.add_argument("--max_new_tokens", type=int, default=512)
    args = ap.parse_args()

    print("[INFO] Loading processor / model ...")
    processor = AutoProcessor.from_pretrained(
        LOCAL_MODEL_DIR,
        trust_remote_code=True,
        local_files_only=True,
        min_pixels=256*28*28,
        max_pixels=1280*28*28,
    )
    processor.tokenizer.padding_side = "left"

    model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
        LOCAL_MODEL_DIR,
        device_map="auto",
        torch_dtype="auto",
        trust_remote_code=True,
        local_files_only=True,
    ).eval()

    img = Image.open(args.image).convert("RGB")

    messages = [{
        "role": "user",
        "content": [
            {"type": "image", "image": img},
            {"type": "text", "text": args.prompt},
        ],
    }]

    inputs = build_inputs(messages, processor, model.device)
    dump_shapes("inputs", inputs)

    print("[INFO] Generating ...")
    try:
        with torch.no_grad():
            out_ids = model.generate(
                **inputs,
                max_new_tokens=args.max_new_tokens,
                temperature=0.1,
                do_sample=False,
                pad_token_id=processor.tokenizer.eos_token_id,
                eos_token_id=processor.tokenizer.eos_token_id,
            )
    except ValueError as e:
        # 专门抓 tokens/features mismatch
        if "Image features and image tokens do not match" in str(e):
            print("[ERR] tokens/features 不匹配：", e)
            print("=> 尝试降低分辨率或增大 max_length 再试")
            return
        raise

    # 切掉prompt
    new_tokens = out_ids[:, inputs["input_ids"].shape[1]:]
    text = processor.batch_decode(new_tokens, skip_special_tokens=True, clean_up_tokenization_spaces=False)[0].strip()

    print("\n===== 识别结果 =====")
    print(text)
    print("====================")

if __name__ == "__main__":
    main()
