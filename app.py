#!/usr/bin/env python3
# coding: utf-8

import os
import time
import json
from pathlib import Path
from werkzeug.utils import secure_filename
from flask import Flask, request, jsonify, render_template, send_file, url_for
import traceback

from web_ocr import WebHandwritingOCR

app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['OUTPUT_FOLDER'] = 'output'

# Create directories if they don't exist
Path(app.config['UPLOAD_FOLDER']).mkdir(exist_ok=True)
Path(app.config['OUTPUT_FOLDER']).mkdir(exist_ok=True)

# Global OCR instance (will be initialized with parameters)
ocr_instance = None

ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'tiff', 'webp'}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def get_ocr_instance(lang='ch', det_thresh=0.3, box_thresh=0.6, prompt=None):
    """Get or create OCR instance with specified parameters"""
    global ocr_instance
    
    if prompt is None:
        if lang == 'ch':
            prompt = "Please recognize the handwriting and output only the text:"
        else:
            prompt = "Please recognize the handwriting and output only the text:"
    
    # Always create a new instance to ensure parameters are updated
    try:
        ocr_instance = WebHandwritingOCR(
            prompt=prompt,
            lang=lang,
            det_thresh=det_thresh,
            box_thresh=box_thresh
        )
        return ocr_instance
    except Exception as e:
        print(f"Error creating OCR instance: {e}")
        raise

@app.route('/')
def index():
    """Main page"""
    return render_template('index.html')

@app.route('/api/upload', methods=['POST'])
def upload_file():
    """Handle file upload and OCR processing"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400
        
        if not allowed_file(file.filename):
            return jsonify({'error': 'Invalid file type'}), 400
        
        # Get parameters from request
        lang = request.form.get('lang', 'ch')
        det_thresh = float(request.form.get('det_thresh', 0.3))
        box_thresh = float(request.form.get('box_thresh', 0.6))
        custom_prompt = request.form.get('prompt', '').strip()
        
        # Save uploaded file
        filename = secure_filename(file.filename)
        timestamp = str(int(time.time()))
        unique_filename = f"{timestamp}_{filename}"
        file_path = Path(app.config['UPLOAD_FOLDER']) / unique_filename
        file.save(file_path)
        
        # Get OCR instance with parameters
        ocr = get_ocr_instance(lang, det_thresh, box_thresh, custom_prompt)
        
        # Process the image
        result = ocr.run_with_visualization(str(file_path))
        
        # Convert absolute paths to relative paths for web serving
        if result.get('annotated_image'):
            result['annotated_image'] = os.path.relpath(result['annotated_image'], start='.')
        
        # Add upload info
        result['upload_filename'] = filename
        result['unique_filename'] = unique_filename
        result['file_path'] = str(file_path)
        
        return jsonify({
            'success': True,
            'result': result
        })
        
    except Exception as e:
        print(f"Error processing upload: {e}")
        print(traceback.format_exc())
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/parameters', methods=['GET'])
def get_parameters():
    """Get available languages and parameter ranges"""
    return jsonify({
        'languages': [
            {'code': 'ch', 'name': 'Chinese'},
            {'code': 'en', 'name': 'English'},
            {'code': 'fr', 'name': 'French'},
            {'code': 'german', 'name': 'German'},
            {'code': 'korean', 'name': 'Korean'},
            {'code': 'japan', 'name': 'Japanese'}
        ],
        'det_thresh_range': {'min': 0.1, 'max': 0.9, 'step': 0.1, 'default': 0.3},
        'box_thresh_range': {'min': 0.1, 'max': 0.9, 'step': 0.1, 'default': 0.6}
    })

@app.route('/uploads/<path:filename>')
def serve_upload(filename):
    """Serve uploaded files"""
    return send_file(Path(app.config['UPLOAD_FOLDER']) / filename)

@app.route('/output/<path:filename>')
def serve_output(filename):
    """Serve output files"""
    file_path = Path(app.config['OUTPUT_FOLDER']) / filename
    if not file_path.exists():
        # Try looking in chain/output directory
        file_path = Path('chain/output') / filename
    return send_file(file_path)

@app.route('/chain/output/<path:filename>')
def serve_chain_output(filename):
    """Serve files from chain/output directory"""
    return send_file(Path('chain/output') / filename)

@app.errorhandler(413)
def too_large(e):
    return jsonify({'error': 'File too large (max 16MB)'}), 413

@app.errorhandler(500)
def internal_error(e):
    return jsonify({'error': 'Internal server error'}), 500

if __name__ == '__main__':
    print("=" * 60)
    print("🚀 Handwriting OCR Web Application")
    print("=" * 60)
    print(f"📁 Upload folder: {Path(app.config['UPLOAD_FOLDER']).absolute()}")
    print(f"📁 Output folder: {Path(app.config['OUTPUT_FOLDER']).absolute()}")
    print("🌐 Starting server...")
    print("=" * 60)
    
    # Run the app
    app.run(host='0.0.0.0', port=5000, debug=True) 